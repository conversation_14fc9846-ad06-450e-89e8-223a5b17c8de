package com.scp.canvas.service

import com.scp.canvas.bean.MyStoryBusinessMetricsBean
import com.scp.canvas.bean.MyStoryPerformanceResultBean
import com.scp.canvas.dao.IMyStoryBusinessMetricsDao
import com.scp.canvas.dao.IMyStoryPerformanceResultDao
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.servlet.ServiceHelper
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service

@Service
@Scope("prototype")
class MyStoryBusinessMetricsService : ServiceHelper() {

    @Resource
    lateinit var myStoryBusinessMetricsDao: IMyStoryBusinessMetricsDao

    @Resource
    lateinit var response: Response

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1(parameterMap: MutableMap<String, Any>): Response {
        val month = parameterMap["month"] as String
        parameterMap["year"] = month.substring(0, 4)
        parameterMap["lastMonth"] = Utils.addMonth(month, -1)
        parameterMap["lastYear"] = Utils.addMonth(month, -12)
        val resultMap = HashMap<String, ArrayList<MyStoryBusinessMetricsBean>>()

        try {
            // OTDS
            this.generateCascaderFilterSQL(parameterMap, null, "OTDS_SOURCE_WEEKLY_V", "T", "_otdsWeeklyFilters")
            this.generateCascaderFilterSQL(parameterMap, null, "OTDS_SOURCE_STANDARD_V", "T", "_otdsStandardFilters")
            if ("fail_lines".equals(parameterMap["otdsType"] as String, ignoreCase = true)) {
                resultMap["otds"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1OTDSFailLines(parameterMap))
            } else {
                resultMap["otds"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1OTDS(parameterMap))
            }
        } catch (ignore: Exception) {

        }


//        parameterMap["category"] = "On Time Confirm"
//        resultMap["otc"] = myStoryPerformanceResultDao.queryReport1ByManualInput(parameterMap)
        try {
            // OTDM
            this.generateCascaderFilterSQL(parameterMap, null, "OTDM_V", "T", "_filters")

            if ("fail_lines".equals(parameterMap["otdmType"] as String, ignoreCase = true)) {
                resultMap["otdm"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1OTDMFailLines(parameterMap))
            } else {
                resultMap["otdm"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1OTDM(parameterMap))
            }
        } catch (ignore: Exception) {

        }

        try {
            // CLO
            this.generateCascaderFilterSQL(parameterMap, null, "MATERIAL_MASTER_V", "T", "_filters")
            resultMap["clo"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1CLO(parameterMap))
        } catch (ignore: Exception) {

        }
        try {
            // PONORO
            this.generateCascaderFilterSQL(parameterMap, null, "COMPLETED_PO_NOR_V", "T", "_filters")
            resultMap["ponoro"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1PONORO(parameterMap))
        } catch (ignore: Exception) {

        }
        try {
            // SONOR
            this.generateCascaderFilterSQL(parameterMap, null, "NOR_V", "T", "_filters")
            resultMap["sonor"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1SONOR(parameterMap))
        } catch (ignore: Exception) {

        }
        try {
            // MyCP
            this.generateCascaderFilterSQL(parameterMap, null, "MYCP_COMMITMENT_V", "T", "_filters")
            resultMap["mycp"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1MyCP(parameterMap))
        } catch (ignore: Exception) {

        }
        try {
            // OTC
            this.generateCascaderFilterSQL(parameterMap, null, "OPM_OTC_DATA_V", "T", "_filters")
            resultMap["otc"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1OTC(parameterMap))
        } catch (ignore: Exception) {

        }
        try {
            // OrderIntake
            this.generateCascaderFilterSQL(parameterMap, null, "DEMAND_ORDER_INTAKE_V", "T", "_filters")
            resultMap["orderIntake"] = this.parseReport1Tree(myStoryBusinessMetricsDao.queryReport1OrderIntake(parameterMap))
        } catch (ignore: Exception) {

        }
        return response.setBody(resultMap)
    }

    fun parseReport1Tree(data: ArrayList<MyStoryBusinessMetricsBean>): ArrayList<MyStoryBusinessMetricsBean> {
        var root = MyStoryBusinessMetricsBean()
        if (data.size > 0) {
            root = data[0]
            root.rowType = "root"
            for (i in 1..<data.size) {
                root.children.add(data[i])
            }
        }
        return ArrayList(listOf(root))
    }
}
