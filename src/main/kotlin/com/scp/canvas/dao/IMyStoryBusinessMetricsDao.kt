package com.scp.canvas.dao

import com.scp.canvas.bean.MyStoryBusinessMetricsBean
import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryBusinessMetricsDao {
    fun queryReport1OTDS(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1OTDSFailLines(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1ByManualInput(parameterMap: MutableMap<String, Any>): MyStoryBusinessMetricsBean

    fun queryReport1OTDM(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1OTDMFailLines(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1CLO(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1PONORO(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1SONOR(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1OTC(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1MyCP(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>

    fun queryReport1OrderIntake(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryBusinessMetricsBean>
}
