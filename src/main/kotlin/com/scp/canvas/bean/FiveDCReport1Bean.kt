package com.scp.canvas.bean

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.starter.utils.Utils
import org.apache.commons.lang3.StringUtils


class FiveDCReport1Bean() {
    var conditions = ArrayList<JSONObject>()
    var tokens = ArrayList<JSONObject>()
    private var alias = ""
    private var caseWhenActive = ""
    private var caseWhenComparer = ""
    private var caseWhenCompareValue = ""
    private var aggFunc = ""

    private var existAlias = ArrayList<String>()

    constructor(value: JSONObject, existAlias: ArrayList<String>) : this() {
        this.existAlias = existAlias
        this.conditions = ArrayList(this.addVirtualNodeToAvoidDivideZero(value.getJSONArray("conditions")).map { e: Any -> (e as JSONObject) }.toList())
        this.tokens = ArrayList(this.addVirtualNodeToAvoidDivideZero(value.getJSONArray("tokens")).map { e: Any -> (e as JSONObject) }.toList())
        this.alias = value.getString("alias")
        this.caseWhenActive = value.getString("caseWhenActive")
        this.caseWhenComparer = value.getString("caseWhenComparer")
        this.caseWhenCompareValue = value.getString("caseWhenCompareValue")
        this.aggFunc = value.getString("aggFunc")

        if (tokens.size == 1 && StringUtils.isBlank(alias)) {
            alias = tokens[0].getJSONArray("value").getJSONArray(0).getString(1)
        } else if (StringUtils.isBlank(alias)) {
            alias = "COL_" + Utils.randomStr(4).uppercase()
        }

        var i = 2
        val orgName = this.alias
        while (existAlias.indexOf(this.alias) != -1) {
            this.alias = orgName + "_" + (i++)
        }
        existAlias.add(this.alias)
    }

    fun getSQL(parameterMap: MutableMap<String, Any>): String {
        val sql = ArrayList<String>()
        sql.add(StringUtils.upperCase(this.aggFunc))
        sql.add("(")
        if ("Y" == this.caseWhenActive) {
            sql.add("CASE WHEN")
            for (token in this.conditions) {
                val type = token.getString("type")
                if ("field" == type) {
                    val tempColumn = token.getJSONArray("value").map { (it as JSONArray).getString(1) }
                    sql.add("NVL(\"" + StringUtils.join(tempColumn, "\", 0) + NVL(\"") + "\", 0)")
                } else {
                    sql.add(token.getString("value"))
                }
            }
            sql.add(this.caseWhenComparer)
            if (this.caseWhenComparer.indexOf(" NULL") == -1) {
                val key = "_" + Utils.randomStr(8)
                sql.add("#{$key, jdbcType=VARCHAR}")
                parameterMap[key] = this.caseWhenCompareValue
            }
            sql.add("THEN")
        }

        // 再拼接value部分的sql
        for (token in this.tokens) {
            val type = token.getString("type")
            if ("field" == type) {
                val tempColumn = token.getJSONArray("value").map { (it as JSONArray).getString(1) }
                sql.add("(NVL(\"" + StringUtils.join(tempColumn, "\", 0) + NVL(\"") + "\", 0))")
            } else {
                sql.add(token.getString("value"))
            }
        }

        if ("Y" == this.caseWhenActive) {
            sql.add("END")
        }
        sql.add(") AS")
        sql.add("\"" + this.alias + "\"")
        return sql.joinToString(" ")
    }

    private fun addVirtualNodeToAvoidDivideZero(tokens: JSONArray): List<JSONObject> {
        val newTokens: MutableList<JSONObject> = ArrayList()
        var i = 0

        while (i < tokens.size) {
            val token = tokens.getJSONObject(i)
            newTokens.add(token)

            if ("operator" == token.getString("type") && "/" == token.getString("value")) {
                // 添加除数前面的虚拟元素
                val beforeDivide = JSONObject()
                beforeDivide["type"] = "addition"
                beforeDivide["value"] = "nullif("
                newTokens.add(beforeDivide)

                // 找到除数的结束位置
                val closeIndex = findCloseIndex(tokens, i + 1)

                // 将 startIndex 到 closeIndex 之间的元素搬运到 newTokens 中
                for (j in i + 1..closeIndex) {
                    newTokens.add(tokens.getJSONObject(j))
                }

                // 添加除数后面的虚拟元素
                val afterDivide = JSONObject()
                afterDivide["type"] = "addition"
                afterDivide["value"] = ", 0)"
                newTokens.add(afterDivide)

                // 跳过已经处理的部分
                i = closeIndex + 1
            } else {
                i++
            }
        }

        if (newTokens.size > 0 && newTokens[newTokens.size - 1].getString("type") == "operator") {
            return newTokens.dropLast(1)
        }
        return newTokens
    }

    private fun findCloseIndex(tokens: JSONArray, startIndex: Int): Int {
        var bracketCount = 0
        for (i in startIndex..<tokens.size) {
            val token = tokens[i] as JSONObject
            if ("bracket" == token.getString("type") && ")" == token.getString("value")) {
                bracketCount--
            } else if ("bracket" == token.getString("type") && "(" == token.getString("value")) {
                bracketCount++
            }
            if (bracketCount == 0) {
                return i
            }
        }
        return tokens.size - 1
    }
}
