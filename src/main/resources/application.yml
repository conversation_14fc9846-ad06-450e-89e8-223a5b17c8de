server:
  port: 8443
  ssl:
    enabled-protocols: TLSv1.2,TLSv1.3
    key-store: classpath:scp-dss.cn.schneider-electric.com.pfx
    #key-store-type: PKCS12T
    key-store-password: "YdGv8D5wnxPKN7qSk64zrf3my"
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
    min-response-size: 1024
  servlet:
    encoding:
      enabled: true
      charset: UTF-8
http:
  port: 8081
logging:
  level:
    root: info
spring:
  application:
    name: scp-service-prod
  serverid: scp02
  monitor: false
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 200MB
  data:
    redis:
      enable: true
      password: 07559f52f261
      host: ************
      database: 0
      port: 6379
      timeout: 60000
      jedis:
        pool:
          min-idle: 2
          max-idle: 8
          max-active: 128
          max-wait: 6000
  datasource:
    base:
      initial-size: 5
      max-active: 32
      min-idle: 2
      max-wait: 120000
      test-on-borrow: true
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 1800000
      min-evictable-idle-time-millis: 25200000
      remove-abandoned: false
      remove-abandoned-timeout: 3600
      log-abandoned: true
      remarks-reporting: true
      filters: stat
      default-row-prefetch: 5000
      driver-class-name: oracle.jdbc.OracleDriver
      validation-query: select sysdate from dual
    scp02:
      url: jdbc:oracle:thin:@************:1521/SCPDB02
      username: scpa
      password: scpaee7e7c93
    scp02-readonly:
      url: jdbc:oracle:thin:@************:1521/SCPDB02
      username: scp_readonly
      password: scpr82b0e1695
    clickhouse:
      url: ***************************************
      username: root
      password: c2ba2739c0f7
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      validation-query: SELECT 1

  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 10000
            readTimeout: 600000
mybatis:
  mapperLocations: classpath*:**/dao/xml/*.xml
session:
  timeout-in-minute: 480
file:
  upload:
    path: "/usr/springboot/upload/"
mail:
  url: "http://10.177.21.9:8083"
kettle:
  url: "http://10.177.21.9:8082"
