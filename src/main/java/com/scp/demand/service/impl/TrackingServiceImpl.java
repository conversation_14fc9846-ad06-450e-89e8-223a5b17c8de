package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.demand.bean.TrackingReport1Bean;
import com.scp.demand.bean.TrackingReport2Bean;
import com.scp.demand.bean.TrackingReport3Bean;
import com.scp.demand.bean.TrackingReport4Bean;
import com.scp.demand.dao.ITrackingDao;
import com.scp.demand.service.ITrackingService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.login.bean.Session;
import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("trackingService")
@Scope("prototype")
@Transactional
public class TrackingServiceImpl implements ITrackingService {

    @Resource
    private Response response;

    @Resource
    private ITrackingDao trackingDao;

    @Resource
    private ExcelTemplate excelTemplate;

    public static final String TRACKING_PARENT_CODE = "menu1A0";

    /**
     * 查询所有的页面需要的信息
     *
     * @return 列表
     */
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> result = new HashMap<>();
        result.put("FCST", trackingDao.queryFcstVersion());
        result.put("LAST_WD_MONTH", trackingDao.queryLastWdMonth());
        result.put("CASCADER", Utils.parseCascader(trackingDao.queryTrackingCascader()));
        result.put("pivotOpts",trackingDao.queryPivotOpts());
        return response.setBody(result);
    }

    // region report1

    /**
     * 查询report1中未展开的部分
     *
     * @param parameterMap 参数
     * @return 结果
     */
    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        // 生成查询列
        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        if (report1Categories.isEmpty()) {
            report1Categories.add("BU");
            report1Categories.add("PRODUCT_LINE");
        }
        parameterMap.put("category", report1Categories.get(0));
        List<String> category7Value = (List<String>) parameterMap.get("category7Value");
        if (category7Value == null || category7Value.isEmpty()) {
            category7Value = new ArrayList<>();
            category7Value.add("UD_NORMAL");
            category7Value.add("UD_MID_AGING");
            category7Value.add("UD_LONG_AGING");
            category7Value.add("UD_CB");
            parameterMap.put("category7Value", category7Value);
        }

        // 计算crd, order intake, sales
        List<TrackingReport1Bean> resultList = trackingDao.queryReport1(parameterMap);

        // 计算fcst
        Map<String, Map<String, Object>> fcstMap = this.queryFCSTSummary((String) parameterMap.get("category"), parameterMap);

        TrackingReport1Bean summary = new TrackingReport1Bean();
        TrackingReport1Bean othersBean = new TrackingReport1Bean();
        for (TrackingReport1Bean bean : resultList) {
            Map<String, Object> fmap = fcstMap.remove(bean.getCategory());
            if (fmap != null) {
                bean.setFcstQty(Utils.parseBigDecimal(fmap.get("QTY")));
                bean.setFcstCostValue(Utils.parseBigDecimal(fmap.get("COST_VALUE")));
                bean.setFcstNetNetValue(Utils.parseBigDecimal(fmap.get("NET_NET_VALUE")));

                bean.setFcstFulfillQty(Utils.parseBigDecimal(fmap.get("FULFILL_QTY")));
                bean.setFcstFulfillCostValue(Utils.parseBigDecimal(fmap.get("FULFILL_COST_VALUE")));
                bean.setFcstFulfillNetNetValue(Utils.parseBigDecimal(fmap.get("FULFILL_NET_NET_VALUE")));
            }

            // 合并FCST的时候, 先不合并Others, 需要将无法归类的所有数据都放到Others之后, 再汇总
            if ("Others".equals(bean.getCategory())) {
                othersBean = bean;
            } else {
                summary.addToSummary(bean);
            }
        }

        if (fcstMap.isEmpty() == false) {
            BigDecimal fcstQty = BigDecimal.ZERO;
            BigDecimal fcstCostValue = BigDecimal.ZERO;
            BigDecimal fcstNetValue = BigDecimal.ZERO;

            BigDecimal fcstFulfillQty = BigDecimal.ZERO;
            BigDecimal fcstFulfillCostValue = BigDecimal.ZERO;
            BigDecimal fcstFulfillNetValue = BigDecimal.ZERO;

            List<String> keys = new ArrayList<>(fcstMap.keySet());
            for (String key : keys) {
                Map<String, Object> fmap = fcstMap.get(key);
                fcstQty = fcstQty.add(Utils.parseBigDecimal(fmap.get("QTY")));
                fcstCostValue = fcstCostValue.add(Utils.parseBigDecimal(fmap.get("COST_VALUE")));
                fcstNetValue = fcstNetValue.add(Utils.parseBigDecimal(fmap.get("NET_NET_VALUE")));

                fcstFulfillQty = fcstFulfillQty.add(Utils.parseBigDecimal(fmap.get("FULFILL_QTY")));
                fcstFulfillCostValue = fcstFulfillCostValue.add(Utils.parseBigDecimal(fmap.get("FULFILL_COST_VALUE")));
                fcstFulfillNetValue = fcstFulfillNetValue.add(Utils.parseBigDecimal(fmap.get("FULFILL_NET_NET_VALUE")));

                TrackingReport1Bean bean = new TrackingReport1Bean();
                bean.setCategory(key);
                bean.setFcstQty(Utils.parseBigDecimal(fmap.get("QTY")));
                bean.setFcstCostValue(Utils.parseBigDecimal(fmap.get("COST_VALUE")));
                bean.setFcstNetNetValue(Utils.parseBigDecimal(fmap.get("NET_NET_VALUE")));

                bean.setFcstFulfillQty(Utils.parseBigDecimal(fmap.get("FULFILL_QTY")));
                bean.setFcstFulfillCostValue(Utils.parseBigDecimal(fmap.get("FULFILL_COST_VALUE")));
                bean.setFcstFulfillNetNetValue(Utils.parseBigDecimal(fmap.get("FULFILL_NET_NET_VALUE")));
                bean.setHasChildren(false);
                resultList.add(bean);
            }

            othersBean.addFcstQty(fcstQty);
            othersBean.addFcstCostValue(fcstCostValue);
            othersBean.addFcstNetNetValue(fcstNetValue);

            othersBean.addFcstFulfillQty(fcstFulfillQty);
            othersBean.addFcstFulfillCostValue(fcstFulfillCostValue);
            othersBean.addFcstFulfillNetNetValue(fcstFulfillNetValue);
        }
        summary.addToSummary(othersBean);

        summary.setCategory("Total");
        summary.setHasChildren(false);
        summary.setParentName(new JSONArray());

        resultList.add(summary);
        return response.setBody(resultList);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateFCSTQueryParameter(parameterMap);

        // 计算crd, order intake, sales
        List<TrackingReport1Bean> dataList = trackingDao.downloadReport1(parameterMap);

        // 计算fcst - 使用多层级分组查询，一次性获取所有组合的fcst数据
        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        Map<String, Map<String, Object>> fcstMap = this.queryFCSTSummaryMultiLevel(parameterMap);

        // 将fcst数据合并到dataList中
        for (TrackingReport1Bean tracking : dataList) {
            // 构建复合键来匹配fcst数据：category1|category2|category3...
            StringBuilder keyBuilder = new StringBuilder();
            if (report1Categories != null && !report1Categories.isEmpty()) {
                for (int i = 0; i < report1Categories.size(); i++) {
                    if (i > 0) keyBuilder.append("|");
                    String categoryValue = null;
                    switch (i) {
                        case 0 -> categoryValue = tracking.getCategory1();
                        case 1 -> categoryValue = tracking.getCategory2();
                        case 2 -> categoryValue = tracking.getCategory3();
                        case 3 -> categoryValue = tracking.getCategory4();
                        case 4 -> categoryValue = tracking.getCategory5();
                    }
                    keyBuilder.append(categoryValue != null ? categoryValue : "Others");
                }
            }

            Map<String, Object> fmap = fcstMap.get(keyBuilder.toString());
            if (fmap != null) {
                tracking.setFcstQty(Utils.parseBigDecimal(fmap.get("QTY")));
                tracking.setFcstCostValue(Utils.parseBigDecimal(fmap.get("COST_VALUE")));
                tracking.setFcstNetNetValue(Utils.parseBigDecimal(fmap.get("NET_NET_VALUE")));

                tracking.setFcstFulfillQty(Utils.parseBigDecimal(fmap.get("FULFILL_QTY")));
                tracking.setFcstFulfillCostValue(Utils.parseBigDecimal(fmap.get("FULFILL_COST_VALUE")));
                tracking.setFcstFulfillNetNetValue(Utils.parseBigDecimal(fmap.get("FULFILL_NET_NET_VALUE")));
            }
        }

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        String type = (String) parameterMap.get("type");
        for (TrackingReport1Bean tracking : dataList) {
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            // 动态添加category字段
            if (report1Categories != null && !report1Categories.isEmpty()) {
                for (int i = 0; i < report1Categories.size(); i++) {
                    String categoryName = report1Categories.getString(i);
                    String categoryValue = null;

                    // 根据索引获取对应的category值
                    switch (i) {
                        case 0 -> categoryValue = tracking.getCategory1();
                        case 1 -> categoryValue = tracking.getCategory2();
                        case 2 -> categoryValue = tracking.getCategory3();
                        case 3 -> categoryValue = tracking.getCategory4();
                        case 4 -> categoryValue = tracking.getCategory5();
                    }

                    if (categoryValue != null) {
                        map.put(categoryName, categoryValue);
                    }
                }
            }
            switch (type) {
                case "Net Net Price", "Net Net Price HKD" -> {
                    map.put("Order Intake", tracking.getOrderIntakeNetNetValue());
                    map.put("CRD", tracking.getCrdNetNetValue());
                    map.put("Sales", tracking.getSalesNetNetValue());
                    map.put("FCST", tracking.getFcstNetNetValue());
                    map.put((String) parameterMap.get("category5Value"), tracking.getBackorderNetNetValue());
                    map.put((String) parameterMap.get("category6Value"), tracking.getBacklogNetNetValue());
                    map.put("UD", tracking.getUdNetNetValue());
                    map.put((String) parameterMap.get("category4Value"), tracking.getSohNetNetValue());
                    map.put("Output", tracking.getOutputNetNetValue());
                }
                case "Moving Average Price" -> {
                    map.put("Order Intake", tracking.getOrderIntakeCostValue());
                    map.put("CRD", tracking.getCrdCostValue());
                    map.put("Sales", tracking.getSalesCostValue());
                    map.put("FCST", tracking.getFcstCostValue());
                    map.put((String) parameterMap.get("category5Value"), tracking.getBackorderCostValue());
                    map.put((String) parameterMap.get("category6Value"), tracking.getBacklogCostValue());
                    map.put("UD", tracking.getUdCostValue());
                    map.put((String) parameterMap.get("category4Value"), tracking.getSohCostValue());
                    map.put("Output", tracking.getOutputCostValue());
                }
                case "Quantity" -> {
                    map.put("Order Intake", tracking.getOrderIntakeQty());
                    map.put("CRD", tracking.getCrdQty());
                    map.put("Sales", tracking.getSalesQty());
                    map.put("FCST", tracking.getFcstQty());
                    map.put((String) parameterMap.get("category5Value"), tracking.getBackorderQty());
                    map.put((String) parameterMap.get("category6Value"), tracking.getBacklogQty());
                    map.put("UD", tracking.getUdQty());
                    map.put((String) parameterMap.get("category4Value"), tracking.getSohQty());
                    map.put("Output", tracking.getOutputQty());
                }
                case "Line" -> {
                    map.put("Order Intake", tracking.getOrderIntakeLine());
                    map.put("CRD", tracking.getCrdLine());
                    map.put("Sales", tracking.getSalesLine());
                    map.put("FCST", tracking.getFcstLine());
                    map.put((String) parameterMap.get("category5Value"), tracking.getBackorderLine());
                    map.put((String) parameterMap.get("category6Value"), tracking.getBacklogLine());
                    map.put("UD", tracking.getUdLine());
                    map.put((String) parameterMap.get("category4Value"), tracking.getSohLine());
                    map.put("Output", tracking.getOutputLine());
                }
            }
            resultList.add(map);
        }

        excelTemplate.create(response, "demand_tracking_" + Utils.randomStr(4) + ".xlsx", resultList);
    }

    /**
     * 查询report1展开的部分
     *
     * @param parameterMap 参数
     * @return 结果
     */
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Sub(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        JSONArray parentName = (JSONArray) parameterMap.get("parentName");
        String expandValue = (String) parameterMap.get("expandValue");
        String expandColumn = report1Categories.getString(parentName.size());
        parameterMap.put("expandValue", expandValue);
        parameterMap.put("expandColumn", expandColumn);
        parameterMap.put("category", report1Categories.getString(parentName.size() + 1));

        List<TrackingReport1Bean> resultList = trackingDao.queryReport1Sub(parameterMap);

        // 计算fcst
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parentName").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }
        Map<String, Map<String, Object>> fcstMap = this.queryFCSTSummary((String) parameterMap.get("category"), parameterMap);

        for (TrackingReport1Bean bean : resultList) {
            if (bean == null) {
                continue;
            }

            Map<String, Object> fmap = fcstMap.remove(bean.getCategory());
            if (fmap != null) {
                bean.setFcstQty(Utils.parseBigDecimal(fmap.get("QTY")));
                bean.setFcstCostValue(Utils.parseBigDecimal(fmap.get("COST_VALUE")));
                bean.setFcstNetNetValue(Utils.parseBigDecimal(fmap.get("NET_NET_VALUE")));

                bean.setFcstFulfillQty(Utils.parseBigDecimal(fmap.get("FULFILL_QTY")));
                bean.setFcstFulfillCostValue(Utils.parseBigDecimal(fmap.get("FULFILL_COST_VALUE")));
                bean.setFcstFulfillNetNetValue(Utils.parseBigDecimal(fmap.get("FULFILL_NET_NET_VALUE")));
            }

            bean.setHasChildren(false);
            bean.setId(Utils.randomStr(12));
            bean.setHasChildren(report1Categories.size() > parentName.size() + 2);
            JSONArray temp = new JSONArray();
            temp.addAll(parentName);
            temp.add(expandValue);
            bean.setParentName(temp);
        }

        // 将没有sales只有fcst的数据添加到表格
        if (fcstMap.isEmpty() == false) {
            for (String key : fcstMap.keySet()) {
                TrackingReport1Bean bean = new TrackingReport1Bean();
                Map<String, Object> fmap = fcstMap.get(key);
                bean.setCategory(key);
                if (fmap != null) {
                    bean.setFcstQty(Utils.parseBigDecimal(fmap.get("QTY")));
                    bean.setFcstCostValue(Utils.parseBigDecimal(fmap.get("COST_VALUE")));
                    bean.setFcstNetNetValue(Utils.parseBigDecimal(fmap.get("NET_NET_VALUE")));

                    bean.setFcstFulfillQty(Utils.parseBigDecimal(fmap.get("FULFILL_QTY")));
                    bean.setFcstFulfillCostValue(Utils.parseBigDecimal(fmap.get("FULFILL_COST_VALUE")));
                    bean.setFcstFulfillNetNetValue(Utils.parseBigDecimal(fmap.get("FULFILL_NET_NET_VALUE")));
                }
                bean.setHasChildren(false);

                resultList.add(bean);
            }
        }

        return response.setBody(resultList);
    }

    /**
     * 右键view details的实现, 仅对crd, order intake和sales生效
     *
     * @param parameterMap 参数
     * @return 结果
     */
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
            parameterMap.put("report1Values", new ArrayList<>());
        } else {
            List<String> values = new ArrayList<>(selectObj.getJSONArray("parentName").toJavaList(String.class));
            values.add(selectObj.getString("category"));
            parameterMap.put("report1Values", values);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);


        switch ((String) parameterMap.get("viewType")) {
            case "Sales" -> parameterMap.put("tableName", "demand_sales_v");
            case "Order Intake" -> parameterMap.put("tableName", "demand_order_intake_v");
            case "CRD" -> parameterMap.put("tableName", "demand_crd_v");
            case "Back Order" -> parameterMap.put("tableName", "demand_back_order_v");
            case "Backlog" -> parameterMap.put("tableName", "demand_backlog_v");
            case "SOH" -> parameterMap.put("tableName", "demand_soh_v");
            case "UD" -> parameterMap.put("tableName", "demand_ud_v");
            case "Output" -> parameterMap.put("tableName", "demand_output_v");
        }

        int total = trackingDao.queryReport1DetailsCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(trackingDao.queryReport1Details(parameterMap));
        }

        return response.setBody(page);
    }

    /**
     * 右键view details的实现, 仅对fcst生效
     *
     * @param parameterMap 参数
     * @return 结果
     */
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1FCSTDetails(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
            parameterMap.put("report1Values", new ArrayList<>());
        } else {
            List<String> values = new ArrayList<>(selectObj.getJSONArray("parentName").toJavaList(String.class));
            values.add(selectObj.getString("category"));
            parameterMap.put("report1Values", values);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateFCSTQueryParameter(parameterMap);

        int total = trackingDao.queryReport1FCSTDetailsCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(trackingDao.queryReport1FCSTDetails(parameterMap));
        }
        return response.setBody(page);
    }

    /**
     * view details中下载的实现, 仅对crd, order intake和sales生效
     *
     * @param parameterMap 参数
     * @param res          HttpServletResponse
     */
    @Override
    public void downloadData(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        this.generateFilter(parameterMap);

        switch ((String) parameterMap.get("viewType")) {
            case "Sales" -> parameterMap.put("tableName", "demand_sales_v");
            case "Order Intake" -> parameterMap.put("tableName", "demand_order_intake_v");
            case "CRD" -> parameterMap.put("tableName", "demand_crd_v");
            case "Back Order" -> parameterMap.put("tableName", "demand_back_order_v");
            case "Backlog" -> parameterMap.put("tableName", "demand_backlog_v");
            case "SOH" -> parameterMap.put("tableName", "demand_soh_v");
            case "UD" -> parameterMap.put("tableName", "demand_ud_v");
            case "Output" -> parameterMap.put("tableName", "demand_output_v");
        }

        String fileName = parameterMap.get("viewType") + "_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ITrackingDao.queryReport1Details", parameterMap);
    }

    /**
     * view details中下载的实现, 仅对fcst生效
     *
     * @param parameterMap 参数
     * @param res          HttpServletResponse
     */
    @Override
    public void downloadFCSTData(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        this.generateFilter(parameterMap);
        this.generateFCSTQueryParameter(parameterMap);
        String fileName = parameterMap.get("viewType") + "_DATA_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ITrackingDao.queryReport1FCSTDetails", parameterMap);
    }

    // endregion

    // region report2

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        String dateType = (String) parameterMap.get("dateType");
        String report2Type = (String) parameterMap.get("report2Type");
        String current = (String) parameterMap.get("saleMonth");
        String last = this.getLastYearAxis(current);

        List<TrackingReport2Bean> currentYearList = this.queryReport2Data(false, current, parameterMap);
        List<TrackingReport2Bean> lastYearList = this.queryReport2Data(true, last, parameterMap);

        Map<String, TrackingReport2Bean> currentYearMap = new HashMap<>();
        Map<String, TrackingReport2Bean> lastYearMap = new HashMap<>();

        for (TrackingReport2Bean bean : currentYearList) {
            currentYearMap.put(bean.getxAxis(), bean);
        }

        for (TrackingReport2Bean bean : lastYearList) {
            lastYearMap.put(bean.getxAxis(), bean);
        }

        List<TrackingReport2Bean> dataList = new ArrayList<>();
        // 为了方便report4计算fcst折线图, 计算每个坐标轴的fcst比例
        // 之所以放在这里, 是不想在report4重新计算一次了
        // demand tracking东拼西凑了好几年, 所以很多东西已经没办法做标准化约束了, 有机会再重构吧
        Map<String, Object> fcstFulfillRatioMap = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        // 先遍历当年记录
        for (TrackingReport2Bean bean : currentYearList) {
            TrackingReport2Bean lastBean = lastYearMap.get(this.getLastYearAxis(bean.getxAxis()));
            TrackingReport2Bean data = new TrackingReport2Bean(bean);

            if (lastBean != null) {
                data.setSalesQtyY_1(lastBean.getSalesQty());
                data.setSalesNetNetValueY_1(lastBean.getSalesNetNetValue());
                data.setSalesCostValueY_1(lastBean.getSalesCostValue());
                data.setOrderIntakeQtyY_1(lastBean.getOrderIntakeQty());
                data.setOrderIntakeNetNetValueY_1(lastBean.getOrderIntakeNetNetValue());
                data.setOrderIntakeCostValueY_1(lastBean.getOrderIntakeCostValue());
                data.setOrderIntakeLineY_1(lastBean.getOrderIntakeLine());
                data.setOutputQtyY_1(lastBean.getOutputQty());
                data.setOutputNetNetValueY_1(lastBean.getOutputNetNetValue());
                data.setOutputCostValueY_1(lastBean.getOutputCostValue());
                data.setOutputLineY_1(lastBean.getOutputLine());
            }
            dataList.add(data);
        }

        // 再遍历历史记录
        if ("fulltime".equals(report2Type)) {
            for (TrackingReport2Bean bean : lastYearList) {
                TrackingReport2Bean currentBean = currentYearMap.get(this.getNextYearAxis(bean.getxAxis()));
                // 只同步当前年没有的信息
                if (currentBean == null) {
                    TrackingReport2Bean data = new TrackingReport2Bean();
                    data.setLastYearData(true);
                    data.setxAxis(bean.getxAxis());
                    data.setSalesQtyY_1(bean.getSalesQty());
                    data.setSalesNetNetValueY_1(bean.getSalesNetNetValue());
                    data.setSalesCostValueY_1(bean.getSalesCostValue());
                    data.setOrderIntakeQtyY_1(bean.getOrderIntakeQty());
                    data.setOrderIntakeNetNetValueY_1(bean.getOrderIntakeNetNetValue());
                    data.setOrderIntakeCostValueY_1(bean.getOrderIntakeCostValue());
                    data.setOrderIntakeLineY_1(bean.getOrderIntakeLine());
                    data.setOutputQtyY_1(bean.getOutputQty());
                    data.setOutputNetNetValueY_1(bean.getOutputNetNetValue());
                    data.setOutputCostValueY_1(bean.getOutputCostValue());
                    data.setOutputLineY_1(bean.getOutputLine());
                    dataList.add(data);
                }
            }
        }

        parameterMap.put("saleMonth", current);
        Map<String, Object> amuMap = this.queryReport2AMU(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parentName").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }
        Map<String, Object> amfMap = this.queryFCSTSummary(null, parameterMap).get("total");

        // 计算月别ADU
        BigDecimal amuQty = Utils.parseBigDecimal(amuMap.get("AMU_QTY"), null);
        BigDecimal amuNetNetValue = Utils.parseBigDecimal(amuMap.get("AMU_NET_NET_VALUE"), null);
        BigDecimal amuCostValue = Utils.parseBigDecimal(amuMap.get("AMU_COST_VALUE"), null);

        BigDecimal amfQty = Utils.parseBigDecimal(amfMap.get("QTY"), null);
        BigDecimal amfNetNetValue = Utils.parseBigDecimal(amfMap.get("NET_NET_VALUE"), null);
        BigDecimal amfCostValue = Utils.parseBigDecimal(amfMap.get("COST_VALUE"), null);

        BigDecimal amfFulfillQty = Utils.parseBigDecimal(amfMap.get("FULFILL_QTY"), null);
        BigDecimal amfFulfillNetNetValue = Utils.parseBigDecimal(amfMap.get("FULFILL_NET_NET_VALUE"), null);
        BigDecimal amfFulfillCostValue = Utils.parseBigDecimal(amfMap.get("FULFILL_COST_VALUE"), null);

        resultMap.put("amfQty", amfQty);
        resultMap.put("amfNetNetValue", amfNetNetValue);
        resultMap.put("amfCostValue", amfCostValue);
        resultMap.put("amfFulfillQty", amfFulfillQty);
        resultMap.put("amfFulfillNetNetValue", amfFulfillNetNetValue);
        resultMap.put("amfFulfillCostValue", amfFulfillCostValue);
        resultMap.put("fcstFulfillRatioMap", fcstFulfillRatioMap);

        BigDecimal totalWorkingDay = BigDecimal.valueOf(this.getTotalWorkingDay(dateType, current));
        BigDecimal totalFulfillWorkingDay = BigDecimal.valueOf(this.getTotalFulfillWorkingDay(dateType, current));

        switch (dateType) {
            case "Month to Date" -> {
                amuQty = amuQty != null ? amuQty.divide(totalWorkingDay, 0, RoundingMode.HALF_UP) : null;
                amuNetNetValue = amuNetNetValue != null ? amuNetNetValue.divide(totalWorkingDay, 0, RoundingMode.HALF_UP) : null;
                amuCostValue = amuCostValue != null ? amuCostValue.divide(totalWorkingDay, 0, RoundingMode.HALF_UP) : null;
                amfQty = amfQty != null ? amfQty.divide(totalWorkingDay, 0, RoundingMode.HALF_UP) : null;
                amfNetNetValue = amfNetNetValue != null ? amfNetNetValue.divide(totalWorkingDay, 0, RoundingMode.HALF_UP) : null;
                amfCostValue = amfCostValue != null ? amfCostValue.divide(totalWorkingDay, 0, RoundingMode.HALF_UP) : null;

                amfFulfillQty = amfQty;
                amfFulfillNetNetValue = amfNetNetValue;
                amfFulfillCostValue = amfCostValue;

                BigDecimal fulfillWorkingDay = BigDecimal.valueOf(trackingDao.getFulfillWorkingDay(current));

                for (TrackingReport2Bean bean : dataList) {
                    if (fulfillWorkingDay.compareTo(BigDecimal.ZERO) == 0) {
                        fcstFulfillRatioMap.put(bean.getxAxis(), 0);
                    } else {
                        fcstFulfillRatioMap.put(bean.getxAxis(), Utils.parseBigDecimal(1).divide(fulfillWorkingDay, 6, RoundingMode.HALF_UP));
                    }
                }
            }
            case "Quarter to Date" -> {
                BigDecimal workingWeek = totalWorkingDay.divide(BigDecimal.valueOf(5), 6, RoundingMode.HALF_UP);
                BigDecimal workingFulfillWeek = totalFulfillWorkingDay.divide(BigDecimal.valueOf(5), 6, RoundingMode.HALF_UP);

                amuQty = amuQty != null ? amuQty.divide(workingWeek, 0, RoundingMode.HALF_UP) : null;
                amuNetNetValue = amuNetNetValue != null ? amuNetNetValue.divide(workingWeek, 0, RoundingMode.HALF_UP) : null;
                amuCostValue = amuCostValue != null ? amuCostValue.divide(workingWeek, 0, RoundingMode.HALF_UP) : null;

                // amf 平摊到每周
                amfQty = amfQty != null ? amfQty.divide(workingWeek, 0, RoundingMode.HALF_UP) : null;
                amfNetNetValue = amfNetNetValue != null ? amfNetNetValue.divide(workingWeek, 0, RoundingMode.HALF_UP) : null;
                amfCostValue = amfCostValue != null ? amfCostValue.divide(workingWeek, 0, RoundingMode.HALF_UP) : null;

                // fulfil的amf只计算到当前月
                amfFulfillQty = amfFulfillQty != null ? amfFulfillQty.divide(workingFulfillWeek, 0, RoundingMode.HALF_UP) : null;
                amfFulfillNetNetValue = amfFulfillNetNetValue != null ? amfFulfillNetNetValue.divide(workingFulfillWeek, 0, RoundingMode.HALF_UP) : null;
                amfFulfillCostValue = amfFulfillCostValue != null ? amfFulfillCostValue.divide(workingFulfillWeek, 0, RoundingMode.HALF_UP) : null;

                // 计算每周的预测
                // 先计算每个月的预测/销售, 然后打散到工作日, 每周有几个工作日, 再计算当周的预测
                // 获取某一个季度, 分布在每一周的ratio
                fcstFulfillRatioMap.putAll(this.queryFcstFulfillRatioList(parameterMap, resultMap));
            }
            case "Year to Date" -> {
                amfQty = amfQty != null ? amfQty.divide(BigDecimal.valueOf(12), 0, RoundingMode.HALF_UP) : null;
                amfNetNetValue = amfNetNetValue != null ? amfNetNetValue.divide(BigDecimal.valueOf(12), 0, RoundingMode.HALF_UP) : null;
                amfCostValue = amfCostValue != null ? amfCostValue.divide(BigDecimal.valueOf(12), 0, RoundingMode.HALF_UP) : null;

                // fulfil的amf只计算到当前月
                BigDecimal workingFulfillMonth = BigDecimal.valueOf(trackingDao.queryWorkingFulfillMonth(current));

                amfFulfillQty = amfFulfillQty != null ? amfFulfillQty.divide(workingFulfillMonth, 0, RoundingMode.HALF_UP) : null;
                amfFulfillNetNetValue = amfFulfillNetNetValue != null ? amfFulfillNetNetValue.divide(workingFulfillMonth, 0, RoundingMode.HALF_UP) : null;
                amfFulfillCostValue = amfFulfillCostValue != null ? amfFulfillCostValue.divide(workingFulfillMonth, 0, RoundingMode.HALF_UP) : null;

                // 获取某一年, 分布在每一月的ratio
                fcstFulfillRatioMap.putAll(this.queryFcstFulfillRatioList(parameterMap, resultMap));
            }
        }

        for (TrackingReport2Bean bean : dataList) {
            bean.setAduQty(this.amuToAdu(amuQty));
            bean.setAduNetNetValue(this.amuToAdu(amuNetNetValue));
            bean.setAduCostValue(this.amuToAdu(amuCostValue));

            bean.setAdfQty(amfQty);
            bean.setAdfNetNetValue(amfNetNetValue);
            bean.setAdfCostValue(amfCostValue);

            bean.setAdfFulfillQty(amfFulfillQty);
            bean.setAdfFulfillNetNetValue(amfFulfillNetNetValue);
            bean.setAdfFulfillCostValue(amfFulfillCostValue);
        }

        // 如果是QTD, 并且选择区间是Q1的时候, 移除W53, W53是上一年带过来的数据
        if ("Quarter to Date".equals(dateType) && StringUtils.endsWith(current, "Q1")) {
            int lastIndex = dataList.size() - 1;
            TrackingReport2Bean lastMonth = dataList.get(lastIndex);
            if (lastMonth.getxAxis().endsWith("W53")) {
                lastMonth.setxAxis(lastMonth.getxAxis().replace("W53", "W00"));
                dataList = dataList.stream().sorted(Comparator.comparing(TrackingReport2Bean::getxAxis)).collect(Collectors.toList());
            }
        }

        resultMap.put("data", dataList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.parseReport2DetailsConditions(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
            parameterMap.put("report1Values", new ArrayList<>());
        } else {
            List<String> values = new ArrayList<>(selectObj.getJSONArray("parentName").toJavaList(String.class));
            values.add(selectObj.getString("category"));
            parameterMap.put("report1Values", values);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        int total = trackingDao.queryReport2DetailsCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(trackingDao.queryReport2Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadData2(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.parseReport2DetailsConditions(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = StringUtils.lowerCase((String) parameterMap.get("viewType")) + "_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ITrackingDao.queryReport2Details", parameterMap);
    }

    private void parseReport2DetailsConditions(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        String type = (String) parameterMap.get("viewType");
        String dateType = (String) parameterMap.get("dateType");
        String saleMonth = (String) parameterMap.get("saleMonth");
        String selectedYear = (String) parameterMap.get("report2SelectedYear");
        String selectedDate = (String) parameterMap.get("report2SelectedDate");

        switch (dateType) {
            // 如果是Month to Date, 需要根据工作日序号找到具体日期
            case "Month to Date" -> {
                // 如果是上一年, 找到上一年的月份
                if (StringUtils.equals(selectedYear, "LAST_YEAR")) {
                    saleMonth = this.getLastYearAxis(saleMonth);
                }
                // 获取工作日序号
                int seq = Utils.parseInt(StringUtils.remove(selectedDate, "WD"), -1);
                List<String> workDays = new ArrayList<>();
                if (seq == -1) {
                    workDays.add("1900/01/01");// 如果获取序号失败, 默认赋值1900/01/01, 这样就没办法通过条件查询出来结果了
                } else {
                    List<Map<String, Object>> wds = trackingDao.queryWorkingDayList(saleMonth.substring(0, 4) + '/' + saleMonth.substring(4));
                    List<String> temp = new ArrayList<>();

                    int wseq = 1;
                    for (Map<String, Object> map : wds) {
                        if (BigDecimal.ONE.compareTo((BigDecimal) map.get("WORKING_DAY")) == 0) {
                            if (seq == wseq) {
                                workDays.add((String) map.get("TEXT"));
                                workDays.addAll(temp);
                                break;
                            }
                            wseq++;
                            temp = new ArrayList<>();
                        } else {// 如果不是working day,将当天数据记录到临时变量中
                            temp.add((String) map.get("TEXT"));
                        }
                    }
                }
                List<String> workDaysList = new ArrayList<>();
                for (String w : workDays) {
                    workDaysList.add("to_date('" + w + "','yyyy/mm/dd')");
                }
                parameterMap.put("column", "CALENDAR_DATE");
                parameterMap.put("saleMonth", StringUtils.join(workDaysList, ","));
            }
            case "Quarter to Date" -> {
                // 如果是上一年, 找到上一年的月份
                if (StringUtils.equals(selectedYear, "LAST_YEAR")) {
                    saleMonth = this.getLastYearAxis(saleMonth);
                }
                parameterMap.put("column", "(SUBSTR(CALENDAR_WEEK, 0,4) || 'W' ||  SUBSTR(CALENDAR_WEEK, 5,2))");
                parameterMap.put("saleMonth", "'" + saleMonth.substring(0, 4) + selectedDate.substring(4) + "'");
            }
            case "Year to Date" -> {
                // 如果是上一年, 找到上一年的月份
                if (StringUtils.equals(selectedYear, "LAST_YEAR")) {
                    saleMonth = this.getLastYearAxis(saleMonth);
                }
                parameterMap.put("column", "CALENDAR_MONTH");
                parameterMap.put("saleMonth", "'" + saleMonth + selectedDate.substring(4) + "'");
            }
        }

        switch (type) {
            case "Sales" -> parameterMap.put("tableName", "demand_sales_v");
            case "Order Intake" -> parameterMap.put("tableName", "demand_order_intake_v");
        }
    }

    // 查询某一季度, 每周的占比
    private Map<String, Object> queryFcstFulfillRatioList(Map<String, Object> parameterMap, Map<String, Object> totalMap) {
        String type = (String) parameterMap.get("type"); // 用户选择的时候Net Net Value还是Cost Value等
        String dateType = (String) parameterMap.get("dateType");
        // 计算分母
        BigDecimal total = switch (type) {
            case "Net Net Price", "Net Net Price HKD" -> new BigDecimal(String.valueOf(totalMap.get("amfFulfillNetNetValue")));
            case "Moving Average Price" -> new BigDecimal(String.valueOf(totalMap.get("amfFulfillCostValue")));
            case "Quantity" -> new BigDecimal(String.valueOf(totalMap.get("amfFulfillQty")));
            default -> BigDecimal.ZERO;
        };

        String netPriceColumn = "avg_selling_price_rmb";

        if (StringUtils.endsWith(type, "HKD") == true) {
            netPriceColumn = "avg_selling_price_hkd";
        }
        parameterMap.put("netPriceColumn", netPriceColumn);
        parameterMap.put("mvpPriceColumn", "unit_cost");

        String columnType = switch (type) {
            case "Net Net Price" -> "T.AVG_SELLING_PRICE_RMB";
            case "Net Net Price HKD" -> "T.AVG_SELLING_PRICE_HKD";
            case "Moving Average Price" -> "T.UNIT_COST";
            default -> "1";
        };

        // 下面代码是获取FCST/Actual在每个月的分布, queryFCSTSummary计算的是总数, 这里我们要拆开计算
        String fcstVersion = (String) parameterMap.get("fcstVersion");
        String saleMonth = ((String) parameterMap.get("saleMonth")).replace("/", "");
        List<String> saleMonthList = new ArrayList<>();
        List<String> fcstFulfillMonthList = new ArrayList<>();

        // 1. 如果fcstVersion大于选择的日期, 说明fcst中没有选择月的预测, 则直接使用实际值
        // 2. 如果fcstVersion等于选择的日期, 说明fcst中month01就是所选月
        // 3. 如果fcstVersion小于选择的日期, 动态计算需要取哪一列, 超过25列取0
        List<String> saleMonths = this.getMonthList(saleMonth);
        // Demand Tracking看的是前一天的数据, 所以取参照时间时, 也要向前多取一位
        Calendar currentCalendar = Calendar.getInstance();
        currentCalendar.add(Calendar.DAY_OF_MONTH, -1);
        String currentMonth = new SimpleDateFormat("yyyyMM").format(currentCalendar.getTime());
        for (String s : saleMonths) {
            if (fcstVersion.compareTo(s) > 0) {
                saleMonthList.add(s);
            } else if (fcstVersion.compareTo(s) == 0) {
                fcstFulfillMonthList.add("'" + fcstVersion + "' AS MONTH, SUM(nvl(T.MONTH01, 0) * " + columnType + ") AS VAL ");
            } else if (fcstVersion.compareTo(s) < 0) {
                int m = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(s)) + 1;
                if (m > 25 || m < 0) {
                    fcstFulfillMonthList.add("0");
                } else {
                    String month = m >= 10 ? "month" + m : "month0" + m;
                    // 在计算Fulfill时, 预测不取未来
                    if (s.compareTo(currentMonth) <= 0) {
                        if (s.compareTo(currentMonth) == 0) {
                            fcstFulfillMonthList.add("'" + s + "' AS MONTH, SUM(" + "NVL(T." + month + ", 0) * " + columnType + ") * " + this.getFcstFulfillMonthRatio(s) + " AS VAL ");
                        } else {
                            fcstFulfillMonthList.add("'" + s + "' AS MONTH, SUM(" + "NVL(T." + month + ", 0) * " + columnType + ") AS VAL ");
                        }
                    }
                }
            }
        }

        Map<String, Object> params = new HashMap<>(parameterMap);
        params.put("fcstFulfillMonthList", fcstFulfillMonthList);
        params.put("saleMonthList", saleMonthList);
        params.put("columnType", columnType);
        params.put("total", total);
        Map<String, Object> workingDayRatioMap = new HashMap<>();
        if ("Quarter to Date".equals(dateType)) {
            List<Map<String, Object>> dataList = trackingDao.queryQuarterFcstByWeek(params);
            for (Map<String, Object> map : dataList) {
                workingDayRatioMap.put((String) map.get("WEEK"), map.get("RATIO"));
            }
        } else if ("Year to Date".equals(dateType)) {
            List<Map<String, Object>> dataList = trackingDao.queryYearFcstByMonth(params);
            for (Map<String, Object> map : dataList) {
                workingDayRatioMap.put((String) map.get("MONTH"), map.get("RATIO"));
            }
        }
        return workingDayRatioMap;
    }

    // endregion

    // region report3

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        String fcstVersion = (String) parameterMap.get("fcstVersion");

        List<String> fcstMonthList = new ArrayList<>();
        List<String> report3Year = (List<String>) parameterMap.get("report3Year");
        List<String> saleMonths = this.getMonthList(report3Year);
        parameterMap.put("report3SaleMonths", saleMonths);

        for (String s : saleMonths) {
            if (fcstVersion.compareTo(s) == 0) {
                fcstMonthList.add("month01");
            } else if (fcstVersion.compareTo(s) < 0) {
                int m = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(s)) + 1;
                if (m > 25 || m < 0) {
                    fcstMonthList.add("0");
                } else {
                    String month = m >= 10 ? "month" + m : "month0" + m;
                    fcstMonthList.add(month);
                }
            }
        }

        List<TrackingReport3Bean> dataList = trackingDao.queryReport3(parameterMap);

        // 将FCST的数据和SALES,CRD数据粘在一起
        Map<String, Object> fcstMap = new HashMap<>();
        if (fcstMonthList.isEmpty() == false) {
            parameterMap.put("fcstMonthList", fcstMonthList);
            fcstMap = trackingDao.queryReport3FCST(parameterMap);
            if (fcstMap == null) {
                fcstMap = new HashMap<>();
            }
        }

        Map<String, TrackingReport3Bean> tempMap = new HashMap<>();
        for (TrackingReport3Bean bean : dataList) {
            tempMap.put(bean.getxAxis(), bean);
        }

        List<TrackingReport3Bean> resultList = new ArrayList<>();

        for (String year : saleMonths) {
            TrackingReport3Bean bean = tempMap.get(year);
            if (bean == null) {
                bean = new TrackingReport3Bean();
                bean.setxAxis(year);
            }

            int gap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(year));
            if (gap > -1) {
                gap = gap + 1;
            }
            String prefix = "MONTH" + (gap < 10 ? "0" + gap : gap);
            bean.setFcstQty(Utils.parseBigDecimal(fcstMap.get(prefix + "_QTY"), bean.getSalesQty()));
            bean.setFcstCostValue(Utils.parseBigDecimal(fcstMap.get(prefix + "_COST_VALUE"), bean.getSalesCostValue()));
            bean.setFcstNetNetValue(Utils.parseBigDecimal(fcstMap.get(prefix + "_NET_NET_VALUE"), bean.getSalesNetNetValue()));

            resultList.add(bean);
        }

        // 如果是QTD, 并且选择区间是Q1的时候, 移除W53, W53是上一年带过来的数据
        if ("Quarter to Date".equals(parameterMap.get("dateType")) && StringUtils.endsWith((String) parameterMap.get("saleMonth"), "Q1") && resultList.isEmpty() == false) {
            int lastIndex = resultList.size() - 1;
            TrackingReport3Bean lastMonth = resultList.get(lastIndex);
            if (lastMonth.getxAxis().endsWith("W53")) {
                lastMonth.setxAxis(lastMonth.getxAxis().replace("W53", "W00"));
                resultList = resultList.stream().sorted(Comparator.comparing(TrackingReport3Bean::getxAxis)).collect(Collectors.toList());
            }
        }

        return response.setBody(resultList);
    }

    // endregion

    // region report4

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        String dateType = (String) parameterMap.get("dateType");
        if ("Month to Date".equals(dateType) == false) {
            return response;
        }
        this.generateFilter(parameterMap);

        String saleMonth = (String) parameterMap.get("saleMonth");

        List<TrackingReport4Bean> resultList = trackingDao.queryReport4(parameterMap);
        Map<String, TrackingReport4Bean> resultMap = new HashMap<>();
        for (TrackingReport4Bean bean : resultList) {
            resultMap.put(bean.getxAxis(), bean);
        }

        Map<String, TrackingReport4Bean> tempMap = new HashMap<>();
        List<Map<String, Object>> workingDayList = trackingDao.queryWorkingDayList(saleMonth.substring(0, 4) + '/' + saleMonth.substring(4));

        int i = 1;
        for (int j = 0; j < workingDayList.size(); j++) {
            Map<String, Object> map = workingDayList.get(j);
            String xAxis = (String) map.get("TEXT");
            TrackingReport4Bean bean = resultMap.getOrDefault(xAxis, new TrackingReport4Bean());
            // 如果是working day, 加入到显示列表
            // 如果不是working day, 什么也不做

            // CRD修正, 将最后一个工作日的CRD改为当月最后一天的CRD, 而不再是最后一个工作日
            if (BigDecimal.ONE.compareTo((BigDecimal) map.get("WORKING_DAY")) == 0 || j == resultList.size() - 1) {
                bean.setxAxis("WD" + (i < 10 ? "0" + i : i));
                tempMap.put(bean.getxAxis(), bean);
                i++;
            }
        }

        return response.setBody(tempMap);
    }

    // endregion

    // region 公共方法
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成Report1排序列
        String report1OrderBy = (String) parameterMap.get("report1OrderBy");
        String type = (String) parameterMap.get("type");

        String netPriceColumn = "avg_selling_price_rmb";

        if (StringUtils.endsWith(type, "HKD") == true) {
            netPriceColumn = "avg_selling_price_hkd";
        }
        parameterMap.put("netPriceColumn", netPriceColumn);
        parameterMap.put("mvpPriceColumn", "unit_cost");

        if (StringUtils.isNotBlank(report1OrderBy) && Utils.hasInjectionAttack(report1OrderBy) == false) {
            if ("Net Net Price".equalsIgnoreCase(type)) {
                parameterMap.put("report1OrderBy", report1OrderBy + "_NET_NET_VALUE");
            } else if ("Moving Average Price".equalsIgnoreCase(type)) {
                parameterMap.put("report1OrderBy", report1OrderBy + "_COST_VALUE");
            } else if ("Net Net Price HKD".equalsIgnoreCase(type)) {
                parameterMap.put("report1OrderBy", report1OrderBy + "_NET_NET_VALUE");
            } else if ("Moving Average Price HKD".equalsIgnoreCase(type)) {
                parameterMap.put("report1OrderBy", report1OrderBy + "_COST_VALUE");
            } else if ("Quantity".equalsIgnoreCase(type)) {
                parameterMap.put("report1OrderBy", report1OrderBy + "_QTY");
            } else if ("Line".equalsIgnoreCase(type)) {
                parameterMap.put("report1OrderBy", report1OrderBy + "_LINE");
            }
        }
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parentName").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }

        // 获取日期计算列和日期默认值
        String dateType = (String) parameterMap.get("dateType");
        String saleMonth = (String) parameterMap.get("saleMonth");
        switch (dateType) {
            case "Month to Date" -> {
                if (StringUtils.isBlank(saleMonth)) {
                    saleMonth = new SimpleDateFormat("yyyyMM").format(new Date());
                }
                parameterMap.put("crdValueColumn", "to_char(DATE$ - 1,'yyyy/mm/dd')");
                parameterMap.put("crdDateColumn", "to_char(DATE$ - 1,'YYYYMM')");
                parameterMap.put("valueColumn", "to_char(CALENDAR_DATE,'yyyy/mm/dd')");
                parameterMap.put("dateColumn", "CALENDAR_MONTH");
            }
            case "Quarter to Date" -> {
                if (StringUtils.isBlank(saleMonth)) {
                    saleMonth = new SimpleDateFormat("yyyy").format(new Date()) + "Q" + this.getQuarter(new SimpleDateFormat("yyyy").format(new Date()));
                }
                parameterMap.put("valueColumn", "SUBSTR(CALENDAR_WEEK, 0,4) || 'W' ||  SUBSTR(CALENDAR_WEEK, 5,2)");
                parameterMap.put("dateColumn", "CALENDAR_QUARTER");
            }
            case "Year to Date" -> {
                if (StringUtils.isBlank(saleMonth)) {
                    saleMonth = new SimpleDateFormat("yyyy").format(new Date());
                }
                parameterMap.put("valueColumn", "CALENDAR_MONTH");
                parameterMap.put("dateColumn", "CALENDAR_YEAR");
            }
        }
        parameterMap.put("saleMonth", saleMonth);

        // 生成筛选条件
        Object categoryObj = parameterMap.get("filterList");

        // 添加内置查询条件
        // 1. 如果AUTH_DETAILS不为空, 则以AUTH_DETAIL设置为主
        //    menu1A0允许的AUTH_DETAILS有两种情况, 即字符串ADMIN和JSON格式的配置字符串, 如{"ENTITY": ["WPF", "SWD"]}
        //    如果AUTH_DETAILS = ADMIN, 可以查看所有内容
        //    如果AUTH_DETAILS 为JSON格式的配置字符串, 只可以查看配置范围内的信息
        //    如果AUTH_DETAILS 没有按照格式配置, 则查询不到任何结果
        //
        // 2. 如果AUTH_DETAILS为空, 则根据USER_MASTER_DATA中的ENTITY进行自动判断
        //    如果ENTITY in (Supply Chain Planning)时, 等同于ADMIN, 可以查看所有信息
        //    如果ENIITY not in (Supply Chain Planning), 系统会根据USER_MASTER_DATA的ENTITY查询, 如果ENTITY不标准, 则查不出任何结果
        Session session = (Session) parameterMap.get("session");
        JSONArray personalArray = new JSONArray();
        if (session != null) {
            String authDetails = StringUtils.trim(trackingDao.queryAuthDetails(session.getUserid(), TRACKING_PARENT_CODE));

            if (StringUtils.isNotBlank(authDetails)) {
                if (StringUtils.equalsIgnoreCase(authDetails, "ADMIN") == false) {
                    try {
                        JSONObject obj = JSON.parseObject(authDetails);
                        for (String key : obj.keySet()) {
                            List<String> valueObj = (List<String>) obj.get(key);
                            for (String value : valueObj) {
                                personalArray.add(new JSONArray() {{
                                    add(key);
                                    add(value);
                                }});
                            }
                        }

                    } catch (Exception ignore) {
                        // 出错的时候随便赋值一个变量,保证查不出来信息即可
                        JSONArray noneArray = new JSONArray() {{
                            add("ENTITY");
                            add("non-ENTITY");
                        }};
                        personalArray.add(noneArray);
                    }
                }
            } else {
                List<String> adminDepts = new ArrayList<>() {{
                    add("Supply Chain Planning");
                    add("SECI");
                }};
                String entity = session.getEntity();
                if (adminDepts.contains(entity) == false) {
                    // 出错的时候随便赋值一个变量,保证查不出来信息即可
                    JSONArray entityArray = new JSONArray() {{
                        add("ENTITY");
                        add(entity);
                    }};
                    personalArray.add(entityArray);
                }
            }
        }

        if (personalArray.isEmpty() == false) {
            Map<String, List<String>> personalFilterMap = new HashMap<>();
            Map<String, List<String>> personalValueMap = new HashMap<>();

            for (Object subObj : personalArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = personalFilterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                List<String> fv = personalValueMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                fv.add(value);
                parameterMap.put(key, value);
            }

            List<String> personalFilterList = new ArrayList<>();
            List<String> personalFilterListWithoutOrderType = new ArrayList<>();

            for (String key : personalFilterMap.keySet()) {
                List<String> fl = personalFilterMap.get(key);
                List<String> fv = personalValueMap.get(key);
                if ("SOH".equalsIgnoreCase(key) == true) {
                    if (fv.contains("With CRD") == true && fv.contains("Without CRD") == false) {
                        parameterMap.put("SOHCRD", "Y");
                    } else if (fv.contains("With CRD") == false && fv.contains("Without CRD") == true) {
                        parameterMap.put("SOHCRD", "N");
                    }
                    continue;
                }
                personalFilterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                if ("ORDER_TYPE".equalsIgnoreCase(key) == false) {
                    personalFilterListWithoutOrderType.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                }
            }

            parameterMap.put("personalFilters", StringUtils.join(personalFilterList, " and "));
            parameterMap.put("personalFilterListWithoutOrderType", StringUtils.join(personalFilterListWithoutOrderType, " and "));
        }

        List<String> shortageLevel = new ArrayList<>();

        JSONArray categoryArray = (JSONArray) categoryObj;
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();
            Map<String, List<String>> valueMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                List<String> fv = valueMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                fv.add(value);
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();
            List<String> filterListWithoutOrderType = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                List<String> fv = valueMap.get(key);

                // 将shortage level单独拎出来, 用来做exists条件查询
                if ("SHORTAGE_LEVEL".equals(key)) {
                    shortageLevel.addAll(fv);
                    continue;
                }

                if ("SOH".equalsIgnoreCase(key) == true) {
                    if (fv.contains("With CRD") == true && fv.contains("Without CRD") == false) {
                        parameterMap.put("SOHCRD", "Y");
                    } else if (fv.contains("With CRD") == false && fv.contains("Without CRD") == true) {
                        parameterMap.put("SOHCRD", "N");
                    }
                    continue;
                }
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                if ("ORDER_TYPE".equalsIgnoreCase(key) == false) {
                    filterListWithoutOrderType.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                }
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
            parameterMap.put("filterListWithoutOrderType", StringUtils.join(filterListWithoutOrderType, " and "));
        }
        parameterMap.put("shortageLevel", shortageLevel);

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        String specialColumn = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialColumn) == false) {
            if (StringUtils.isNotBlank(specialContent)) {
                parameterMap.put("specialList", Utils.splitValue(specialContent));
                parameterMap.put("specialColumn", specialColumn);
            }
        }

        // soh scope
        String scope = (String) parameterMap.get("scope");
        List<String> subScope = (List<String>) parameterMap.get("subScope");
        List<String> sohScopeList = new ArrayList<>();
        if ("SECI".equals(scope)) {
            sohScopeList.add("SECI");
            if (subScope != null && (subScope.contains("SEHK_IG") || subScope.contains("SEHK_OG"))) {
                sohScopeList.add("SEHK");
            }
            parameterMap.put("sohScopeFilter", "T.SE_SCOPE IN ('" + StringUtils.join(sohScopeList, "','") + "')");
        } else if ("PLANT".equals(scope)) {
            if (subScope == null || subScope.isEmpty()) {
                parameterMap.put("sohScopeFilter", "T.PLANT_SCOPE IS NOT NULL");
            } else {
                List<String> scList = new ArrayList<>();
                for (String s : subScope) {
                    String key = Utils.randomStr(8);
                    parameterMap.put(key, s);
                    scList.add("#{" + key + ", jdbcType=VARCHAR}");
                }
                parameterMap.put("sohScopeFilter", "T.PLANT_SCOPE IN (" + StringUtils.join(scList, ",") + ")");
            }
        }

        // soh qty
        String category4Value = (String) parameterMap.get("category4Value");
        String sohQtyColumn = switch (category4Value) {
            case "SOH Aging <1Y" -> "(order_quantity - stock_1y_2y - stock_gt_2y)";
            case "SOH Aging 1-2Y" -> "stock_1y_2y";
            case "SOH Aging >2Y" -> "stock_gt_2y";
            default -> "order_quantity";
        };

        parameterMap.put("sohQtyColumn", sohQtyColumn);

        // backorder & backlog
        String report1BacklogType = (String) parameterMap.get("report1BacklogType");
        if ("OPEN_SO_W_O_GI".equals(report1BacklogType)) {
            parameterMap.put("backorderQtyColumn", "t.order_quantity");
            parameterMap.put("backlogQtyColumn", "t.open_so_w_o_gi");
        } else if ("OPEN_SO_W_O_DEL".equals(report1BacklogType)) {
            parameterMap.put("backorderQtyColumn", "t.open_so_w_o_del");
            parameterMap.put("backlogQtyColumn", "t.open_so_w_o_del");
        } else {
            parameterMap.put("backorderQtyColumn", "0");
            parameterMap.put("backlogQtyColumn", "0");
        }
    }

    private String getLastYearAxis(String axis) {
        if (axis.startsWith("WD")) {
            return axis;
        } else {
            return (Utils.parseInt(axis.substring(0, 4)) - 1) + axis.substring(4);
        }
    }

    private String getNextYearAxis(String axis) {
        if (axis.startsWith("WD")) {
            return axis;
        } else {
            return (Utils.parseInt(axis.substring(0, 4)) + 1) + axis.substring(4);
        }
    }

    private List<TrackingReport2Bean> queryReport2Data(boolean isLastYear, String saleMonth, Map<String, Object> parameterMap) {
        parameterMap.put("saleMonth", saleMonth);
        String dateType = (String) parameterMap.get("dateType");

        parameterMap.remove("isLastYear");
        if ("Year to Date".equals(dateType)) {
            parameterMap.put("isLastYear", isLastYear ? "Y" : "N");
        }

        // 非工作日向后计算至最近的一个工作日
        List<TrackingReport2Bean> resultList = trackingDao.queryReport2(parameterMap);
        if (dateType.equals("Month to Date")) {
            List<Map<String, Object>> workingDayList = trackingDao.queryWorkingDayList(saleMonth.substring(0, 4) + '/' + saleMonth.substring(4));

            Map<String, TrackingReport2Bean> workingDayMap = new HashMap<>();
            for (TrackingReport2Bean bean : resultList) {
                workingDayMap.put(bean.getxAxis(), bean);
            }

            List<TrackingReport2Bean> tempList = new ArrayList<>();
            TrackingReport2Bean temp = new TrackingReport2Bean();
            String today = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

            int i = 1;
            for (Map<String, Object> map : workingDayList) {
                String xAxis = (String) map.get("TEXT");
                if (xAxis.compareTo(today) >= 0) {
                    break;
                }
                TrackingReport2Bean bean = workingDayMap.getOrDefault(xAxis, new TrackingReport2Bean());
                // 如果是working day
                if (BigDecimal.ONE.compareTo((BigDecimal) map.get("WORKING_DAY")) == 0) {
                    bean.add(temp);
                    bean.setxAxis("WD" + (i < 10 ? "0" + i : i));
                    tempList.add(bean);
                    temp = new TrackingReport2Bean();
                    i++;
                } else {// 如果不是working day,将当天数据记录到临时变量中
                    temp.add(bean);
                }
            }

            // 如果区间的最后几天是周末, 那么把周末的数据归到区间最后一个工作日
            if (temp.isInitial() == false && tempList.isEmpty() == false) {
                tempList.get(tempList.size() - 1).add(temp);
            }

            return tempList;
        } else if (dateType.equals("Quarter to Date")) {
            // 如果某季度的第一周全部是周末或那一周没有查询出相应的结果, 就会导致xAxis缺少对应的值
            // 所以需要在这里补全一下xAxis
            List<String> quarterWeekList = trackingDao.queryQuarterWeekList(saleMonth.substring(0, 4), saleMonth.substring(5));
            Map<String, TrackingReport2Bean> weekMap = new HashMap<>();
            for (TrackingReport2Bean bean : resultList) {
                weekMap.put(bean.getxAxis(), bean);
            }

            String thisWeek = trackingDao.queryThisWeek();
            List<TrackingReport2Bean> tempList = new ArrayList<>();
            // 数据最多展示到当前周, 未来的数据不展示
            // SAP中会有发生在未来的销售, 鬼知道是为什么
            for (String week : quarterWeekList) {
                if (week.compareTo(thisWeek) > 0) {
                    break;
                }
                TrackingReport2Bean t2 = weekMap.remove(week);
                if (t2 != null) {
                    tempList.add(t2);
                } else {
                    TrackingReport2Bean temp = new TrackingReport2Bean();
                    temp.setxAxis(week);
                    tempList.add(temp);
                }
            }
            return tempList;
        } else {
            return resultList;
        }
    }

    /**
     * 根据查询条件计算对应的FCST
     *
     * @param groupby      按哪一列分组, 如果为null, 则显示汇总供ADF使用, 如果有分组列, 则按分组列分组显示, 供report1, report3 使用
     * @param parameterMap 前台参数
     * @return 如果groupbyColumn is null, 则rootmap只有一个key(total) , 如果有分组列, 则按照分组列统计
     */
    private Map<String, Map<String, Object>> queryFCSTSummary(String groupby, Map<String, Object> parameterMap) {
        if ("ship_to_region".equalsIgnoreCase(groupby)) {
            return new HashMap<>();
        }
        parameterMap.put("groupby", groupby);

        this.generateFCSTQueryParameter(parameterMap);

        List<Map<String, Object>> result = trackingDao.queryFCSTData(parameterMap);
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        if (StringUtils.isBlank(groupby) == true) {
            resultMap.put("total", (result.isEmpty() || result.get(0) == null) ? new HashMap<>() : result.get(0));
        } else {
            for (Map<String, Object> map : result) {
                resultMap.put((String) map.get("CATEGORY"), map);
            }
        }

        return resultMap;
    }

    /**
     * 根据查询条件计算对应的FCST - 支持多层级分组
     *
     * @param parameterMap 前台参数
     * @return 按多个category组合分组的fcst数据
     */
    private Map<String, Map<String, Object>> queryFCSTSummaryMultiLevel(Map<String, Object> parameterMap) {
        this.generateFCSTQueryParameter(parameterMap);

        List<Map<String, Object>> result = trackingDao.queryFCSTDataMultiLevel(parameterMap);
        Map<String, Map<String, Object>> resultMap = new HashMap<>();

        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        for (Map<String, Object> map : result) {
            // 构建复合键：category1|category2|category3...
            StringBuilder keyBuilder = new StringBuilder();
            if (report1Categories != null) {
                for (int i = 0; i < report1Categories.size(); i++) {
                    if (i > 0) keyBuilder.append("|");
                    String categoryValue = (String) map.get("CATEGORY" + (i + 1));
                    keyBuilder.append(categoryValue != null ? categoryValue : "Others");
                }
            }
            resultMap.put(keyBuilder.toString(), map);
        }

        return resultMap;
    }

    /**
     * 生成查询FCST必要的查询条件
     *
     * @param parameterMap 输入也是输出的对象
     */
    private void generateFCSTQueryParameter(Map<String, Object> parameterMap) {
        String fcstVersion = (String) parameterMap.get("fcstVersion");

        String saleMonth = ((String) parameterMap.get("saleMonth")).replace("/", "");
        List<String> saleMonthList = new ArrayList<>();
        List<String> fcstMonthList = new ArrayList<>();
        List<String> fcstFulfillMonthList = new ArrayList<>();


        // 1. 如果fcstVersion大于选择的日期, 说明fcst中没有选择月的预测, 则直接使用实际值
        // 2. 如果fcstVersion等于选择的日期, 说明fcst中month01就是所选月
        // 3. 如果fcstVersion小于选择的日期, 动态计算需要取哪一列, 超过25列取0
        List<String> saleMonths = this.getMonthList(saleMonth);
        // Demand Tracking看的是前一天的数据, 所以取参照时间时, 也要向前多取一位
        Calendar currentCalendar = Calendar.getInstance();
        currentCalendar.add(Calendar.DAY_OF_MONTH, -1);
        String currentMonth = new SimpleDateFormat("yyyyMM").format(currentCalendar.getTime());
        for (String s : saleMonths) {
            if (fcstVersion.compareTo(s) > 0) {
                saleMonthList.add(s);
            } else if (fcstVersion.compareTo(s) == 0) {
                fcstMonthList.add("nvl(t.month01,0)");
                fcstFulfillMonthList.add("(nvl(t.month01,0) * " + this.getFcstFulfillMonthRatio(s) + ")");
            } else if (fcstVersion.compareTo(s) < 0) {
                int m = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(s)) + 1;
                if (m > 25 || m < 0) {
                    fcstMonthList.add("0");
                    fcstFulfillMonthList.add("0");
                } else {
                    String month = m >= 10 ? "month" + m : "month0" + m;
                    fcstMonthList.add("nvl(t." + month + ",0)");
                    // 在计算Fulfill时, 预测不取未来
                    if (s.compareTo(currentMonth) <= 0) {
                        if (s.compareTo(currentMonth) == 0) {
                            fcstFulfillMonthList.add("(nvl(t." + month + ",0) * " + this.getFcstFulfillMonthRatio(s) + ")");
                        } else {
                            fcstFulfillMonthList.add("nvl(t." + month + ",0)");
                        }
                    }
                }
            }
        }

        parameterMap.put("fcstMonths", fcstMonthList.isEmpty() ? "0" : StringUtils.join(fcstMonthList, " + "));
        parameterMap.put("fcstFulfillMonths", fcstFulfillMonthList.isEmpty() ? "0" : StringUtils.join(fcstFulfillMonthList, " + "));
        parameterMap.put("saleMonthList", saleMonthList);
    }

    private Map<String, Object> queryReport2AMU(Map<String, Object> parameterMap) {
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parentName").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }
        Map<String, Object> result = trackingDao.queryReport2AMU(parameterMap);
        return result == null ? new HashMap<>() : result;
    }

    private List<String> getMonthList(List<String> saleMonth) {
        saleMonth = saleMonth.stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
        List<String> resultList = new ArrayList<>();
        for (String sale : saleMonth) {
            resultList.addAll(this.getMonthList(sale));
        }
        return resultList;
    }

    /**
     * 计算日期中包含哪些月
     *
     * @param saleMonth 月或年或季度
     * @return 月份List
     */
    private List<String> getMonthList(String saleMonth) {
        List<String> resultList = new ArrayList<>();
        if (saleMonth.contains("Q")) {
            String[] ms = saleMonth.split("Q");
            switch (ms[1]) {
                case "1" -> {
                    resultList.add(ms[0] + "01");
                    resultList.add(ms[0] + "02");
                    resultList.add(ms[0] + "03");
                }
                case "2" -> {
                    resultList.add(ms[0] + "04");
                    resultList.add(ms[0] + "05");
                    resultList.add(ms[0] + "06");
                }
                case "3" -> {
                    resultList.add(ms[0] + "07");
                    resultList.add(ms[0] + "08");
                    resultList.add(ms[0] + "09");
                }
                case "4" -> {
                    resultList.add(ms[0] + "10");
                    resultList.add(ms[0] + "11");
                    resultList.add(ms[0] + "12");
                }
            }
        } else if (saleMonth.length() == 4) {
            for (int i = 1; i <= 12; i++) {
                resultList.add(saleMonth + (i < 10 ? "0" + i : i));
            }
        } else {
            resultList.add(saleMonth);
        }

        return resultList;
    }

    private String getQuarter(String month) {
        return String.valueOf((int) Math.ceil(Utils.parseDouble(month) / 3));
    }

    private BigDecimal amuToAdu(BigDecimal amu) {
        if (amu == null) {
            return null;
        }
        return amu.divide(BigDecimal.valueOf(21), 2, RoundingMode.HALF_UP);
    }

    // 获取规定时间内所有的工作日天数
    private int getTotalWorkingDay(String dateType, String month) {
        Map<String, String> param = new HashMap<>();
        String column = switch (dateType) {
            case "Month to Date" -> "to_char(to_date(text,'yyyy/mm/dd'),'yyyymm')";
            case "Quarter to Date" -> "to_char(to_date(text,'yyyy/mm/dd'),'yyyy\"Q\"q')";
            case "Year to Date" -> "to_char(to_date(text,'yyyy/mm/dd'),'yyyy')";
            default -> "";
        };
        param.put("column", column);
        param.put("month", month);

        return trackingDao.getTotalWorkingDay(param);
    }

    private int getTotalFulfillWorkingDay(String dateType, String month) {
        Map<String, String> param = new HashMap<>();
        String column = switch (dateType) {
            case "Month to Date" -> "to_char(to_date(text,'yyyy/mm/dd'),'yyyymm')";
            case "Quarter to Date" -> "to_char(to_date(text,'yyyy/mm/dd'),'yyyy\"Q\"q')";
            case "Year to Date" -> "to_char(to_date(text,'yyyy/mm/dd'),'yyyy')";
            default -> "";
        };
        param.put("column", column);
        param.put("month", month);

        return trackingDao.getTotalFulfillWorkingDay(param);
    }

    private double getFcstFulfillMonthRatio(String baseMonth) {
        String currentMonth = new SimpleDateFormat("yyyyMM").format(new Date());
        // 如果当前月大于基准月, 说明基准月在历史, 不需要按工作日折扣
        if (currentMonth.compareTo(baseMonth) > 0) {
            return 1;
        } else if (currentMonth.compareTo(baseMonth) == 0) {
            // 如果当前月等于基准月, 需要按照当前工作日进行按比例折扣
            return trackingDao.getLastWDMonthRatio();
        } else if (currentMonth.compareTo(baseMonth) < 0) {
            // 如果基准月在未来, 则不需要计算FCST, 返回0
            return 0;
        }
        return 1;
    }

    // endregion
}
