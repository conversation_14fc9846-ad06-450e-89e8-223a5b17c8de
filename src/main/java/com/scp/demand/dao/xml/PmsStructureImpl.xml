<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IPmsStructureDao">
    <sql id="pmsFilter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        AND ${dateColumn} BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        <if test="scopeFilter != null and scopeFilter != ''.toString()">
            AND ${scopeFilter}
        </if>
        <if test="personalFilters != null and personalFilters != ''.toString()">
            and ${personalFilters}
        </if>
    </sql>

    <select id="queryDateColumns" resultMap="columnMap" resultType="java.util.List">
        SELECT distinct COLUMN_NAME
        FROM USER_TAB_COLS
        WHERE TABLE_NAME  = 'PMS_STRUCTURE_V'
          AND DATA_TYPE = 'DATE'
          AND COLUMN_NAME NOT IN ('CREATE_DATE$', 'UPDATE_DATE$')
        ORDER BY COLUMN_NAME
    </select>

    <resultMap id="columnMap" type="java.lang.String">
        <result column="COLUMN_NAME"/>
    </resultMap>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM PMS_STRUCTURE_FILTER_V
        ORDER BY CATEGORY,NAME
    </select>

    <resultMap id="report1ResultMap" type="com.scp.demand.bean.PmsReport1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.demand.bean.PmsReport1Tooltips">
            <result property="NET_NET_VALUE_RMB" column="NET_NET_VALUE_RMB"/>
            <result property="LINE" column="LINE"/>
        </association>
    </resultMap>

    <select id="queryReport1" resultMap="report1ResultMap">
        SELECT
            NVL(${level1}, 'Others') AS CATEGORY1,
            NVL(${level2}, 'Others') AS CATEGORY2,
            NVL(${level3}, 'Others') AS CATEGORY3,
            NVL(${level4}, 'Others') AS CATEGORY4,
            NVL(${level5}, 'Others') AS CATEGORY5,
            ${valueColumn}           AS VALUE
        <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
            ,${tooltipsColumns}
        </if>
        FROM ${SCPA.PMS_STRUCTURE_V} T
        <where>
            <include refid="pmsFilter"/>
        </where>
        GROUP BY ${level1},${level2},${level3},${level4},${level5}
    </select>

    <resultMap id="report2ResultMap" type="com.scp.demand.bean.PmsReport2Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport2" parameterType="java.util.Map" resultMap="report2ResultMap">
        WITH BASE AS (
            SELECT /*+ parallel(t 6)*/ * FROM ${SCPA.PMS_STRUCTURE_V} T
        )
        <choose>
            <when test='report2SelectedType == "VIEW_BY_DAY".toString()'>
                SELECT TO_CHAR(PMS_CREATED_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE,
                        <choose>
                            <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                                'PMS_CREATED_DATE' AS NAME,
                            </when>
                            <otherwise>
                                NVL(t.${report2ViewType}, 'Others') AS NAME,
                            </otherwise>
                        </choose>
                        ROUND(${valueColumn}, 2) AS VALUE
                FROM BASE T
                <where>
                    <include refid="pmsFilter"/>
                </where>
                GROUP BY TO_CHAR(PMS_CREATED_DATE, 'yyyy/mm/dd'),
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE'
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others')
                    </otherwise>
                </choose>
                ORDER BY TO_CHAR(PMS_CREATED_DATE, 'yyyy/mm/dd') DESC, DECODE(
                NVL(T.${report2ViewType}, 'Others'),
                '0D', 'A',
                '0-1D', 'B',
                '1-2D', 'C',
                '2-3D', 'D',
                '3-7D', 'E',
                '5-7D', 'F',
                '3-4D', 'G',
                '0-1W', 'H',
                '1-2W', 'I',
                '2-4W', 'J',
                '1-2M', 'K',
                '2-3M', 'L',
                '3-6M', 'M',
                '6-12M', 'N',
                '6-1Y', 'O',
                '1-2Y', 'P',
                '>2Y', 'Q',
                'Others', 'R', NVL(T.${report2ViewType}, 'Others')
                )
            </when>
            <when test='report2SelectedType == "VIEW_BY_WEEK".toString()'>
                SELECT CALENDAR_WEEK AS CALENDAR_DATE,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE' AS NAME,
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others') AS NAME,
                    </otherwise>
                </choose>
                ROUND(${valueColumn}, 2) AS VALUE
                FROM BASE T
                <where>
                    <include refid="pmsFilter"/>
                </where>
                GROUP BY CALENDAR_WEEK,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE'
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others')
                    </otherwise>
                </choose>
                ORDER BY CALENDAR_WEEK DESC, DECODE(
                NVL(T.${report2ViewType}, 'Others'),
                '0D', 'A',
                '0-1D', 'B',
                '1-2D', 'C',
                '2-3D', 'D',
                '3-7D', 'E',
                '5-7D', 'F',
                '3-4D', 'G',
                '0-1W', 'H',
                '1-2W', 'I',
                '2-4W', 'J',
                '1-2M', 'K',
                '2-3M', 'L',
                '3-6M', 'M',
                '6-12M', 'N',
                '6-1Y', 'O',
                '1-2Y', 'P',
                '>2Y', 'Q',
                'Others', 'R', NVL(T.${report2ViewType}, 'Others')
                )
            </when>
            <when test='report2SelectedType == "VIEW_BY_MONTH".toString()'>
                SELECT CALENDAR_MONTH AS CALENDAR_DATE,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE' AS NAME,
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others') AS NAME,
                    </otherwise>
                </choose>
                ROUND(${valueColumn}, 2) AS VALUE
                FROM BASE T
                <where>
                    <include refid="pmsFilter"/>
                </where>
                GROUP BY CALENDAR_MONTH,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE'
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others')
                    </otherwise>
                </choose>
                ORDER BY CALENDAR_MONTH DESC, DECODE(
                NVL(T.${report2ViewType}, 'Others'),
                '0D', 'A',
                '0-1D', 'B',
                '1-2D', 'C',
                '2-3D', 'D',
                '3-7D', 'E',
                '5-7D', 'F',
                '3-4D', 'G',
                '0-1W', 'H',
                '1-2W', 'I',
                '2-4W', 'J',
                '1-2M', 'K',
                '2-3M', 'L',
                '3-6M', 'M',
                '6-12M', 'N',
                '6-1Y', 'O',
                '1-2Y', 'P',
                '>2Y', 'Q',
                'Others', 'R', NVL(T.${report2ViewType}, 'Others')
                )
            </when>
            <when test='report2SelectedType == "VIEW_BY_QUARTER".toString()'>
                SELECT CALENDAR_QUARTER AS CALENDAR_DATE,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE' AS NAME,
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others') AS NAME,
                    </otherwise>
                </choose>
                ROUND(${valueColumn}, 2) AS VALUE
                FROM BASE T
                <where>
                    <include refid="pmsFilter"/>
                </where>
                GROUP BY CALENDAR_QUARTER,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE'
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others')
                    </otherwise>
                </choose>
                ORDER BY CALENDAR_QUARTER DESC, DECODE(
                NVL(T.${report2ViewType}, 'Others'),
                '0D', 'A',
                '0-1D', 'B',
                '1-2D', 'C',
                '2-3D', 'D',
                '3-7D', 'E',
                '5-7D', 'F',
                '3-4D', 'G',
                '0-1W', 'H',
                '1-2W', 'I',
                '2-4W', 'J',
                '1-2M', 'K',
                '2-3M', 'L',
                '3-6M', 'M',
                '6-12M', 'N',
                '6-1Y', 'O',
                '1-2Y', 'P',
                '>2Y', 'Q',
                'Others', 'R', NVL(T.${report2ViewType}, 'Others')
                )
            </when>
            <when test='report2SelectedType == "VIEW_BY_YEAR".toString()'>
                SELECT CALENDAR_YEAR AS CALENDAR_DATE,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE' AS NAME,
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others') AS NAME,
                    </otherwise>
                </choose>
                ROUND(${valueColumn}, 2) AS VALUE
                FROM BASE T
                <where>
                    <include refid="pmsFilter"/>
                </where>
                GROUP BY CALENDAR_YEAR,
                <choose>
                    <when test="report2ViewType == 'PMS_CREATED_DATE'.toString()">
                        'PMS_CREATED_DATE'
                    </when>
                    <otherwise>
                        NVL(t.${report2ViewType}, 'Others')
                    </otherwise>
                </choose>
                ORDER BY CALENDAR_YEAR DESC, DECODE(
                NVL(T.${report2ViewType}, 'Others'),
                '0D', 'A',
                '0-1D', 'B',
                '1-2D', 'C',
                '2-3D', 'D',
                '3-7D', 'E',
                '5-7D', 'F',
                '3-4D', 'G',
                '0-1W', 'H',
                '1-2W', 'I',
                '2-4W', 'J',
                '1-2M', 'K',
                '2-3M', 'L',
                '3-6M', 'M',
                '6-12M', 'N',
                '6-1Y', 'O',
                '1-2Y', 'P',
                '>2Y', 'Q',
                'Others', 'R', NVL(T.${report2ViewType}, 'Others')
                )
            </when>
        </choose>
    </select>

    <sql id="reportDateFilter">
        <choose>
            <when test="${viewType} == 'VIEW_BY_DAY'.toString()">
                T.${viewByDay} = TO_DATE(#{${selectedDate}, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <when test="${viewType} == 'VIEW_BY_WEEK'.toString()">
                T.${viewByWeek} = #{${selectedDate}, jdbcType=VARCHAR}
            </when>
            <when test="${viewType} == 'VIEW_BY_MONTH'.toString()">
                T.${viewByMonth} = #{${selectedDate}, jdbcType=VARCHAR}
            </when>
            <when test="${viewType} == 'VIEW_BY_QUARTER'.toString()">
                T.${viewByQuarter} = #{${selectedDate}, jdbcType=VARCHAR}
            </when>
            <when test="${viewType} == 'VIEW_BY_YEAR'.toString()">
                T.${viewByYear} = #{${selectedDate}, jdbcType=VARCHAR}
            </when>
        </choose>
    </sql>

    <sql id="report2DetailsSQL">
        SELECT *
        FROM ${SCPA.PMS_STRUCTURE_V} T
        <where>
            <include refid="reportDateFilter">
                <property name="selectedDate" value="report2SelectedValue"/>
                <property name="dateRange" value="PMS_CREATED_DATE"/>
                <property name="viewByDay" value="CALENDAR_DATE"/>
                <property name="viewByWeek" value="CALENDAR_WEEK"/>
                <property name="viewByMonth" value="CALENDAR_MONTH"/>
                <property name="viewByQuarter" value="CALENDAR_QUARTER"/>
                <property name="viewByYear" value="CALENDAR_YEAR"/>
                <property name="viewType" value="report2SelectedType"/>
            </include>
            <include refid="pmsFilter"/>
        </where>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3SQL">WITH BASE AS (
        SELECT /*+ parallel(t 6)*/ * FROM ${SCPA.PMS_STRUCTURE_V} T
        <where>
            <include refid="pmsFilter"/>
        </where>
        ), NON_ABLA AS (
        SELECT  <foreach collection="report3SelectedColumns" item="item">
                    NVL(${item}, 'Others')                                    AS ${item},
                </foreach>
                <choose>
                    <when test="report3ViewType == 'VIEW_BY_DAY'.toString()">
                        T.PMS_CREATED_DATE                                AS PMS_CREATED_DATE,
                        ROUND(${valueColumn}, 3)                         AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        PMS_CREATED_DATE
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_WEEK'.toString()">
                        T.CALENDAR_WEEK,
                        ROUND(${valueColumn}, 3)                         AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        CALENDAR_WEEK
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_MONTH'.toString()">
                        T.CALENDAR_MONTH,
                        ROUND(${valueColumn}, 3)                          AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        CALENDAR_MONTH
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_QUARTER'.toString()">
                        T.CALENDAR_QUARTER,
                        ROUND(${valueColumn}, 3)                            AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        CALENDAR_QUARTER
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_YEAR'.toString()">
                        T.CALENDAR_YEAR,
                        ROUND(${valueColumn}, 3)                         AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        CALENDAR_YEAR
                        )
                    </when>
                </choose>
        SELECT *
        FROM (
            SELECT
            <foreach collection="report3SelectedColumns" item="item">
                NVL(${item}, 'Others')                              AS ${item},
            </foreach>
            <choose>
                <when test="report3ViewType == 'VIEW_BY_DAY'.toString()">
                    TO_CHAR(T.PMS_CREATED_DATE,'YYYY/MM/DD')         AS CALENDAR_DATE,
                </when>
                <when test="report3ViewType == 'VIEW_BY_WEEK'.toString()">
                    T.CALENDAR_WEEK                               AS CALENDAR_DATE,
                </when>
                <when test="report3ViewType == 'VIEW_BY_MONTH'.toString()">
                    T.CALENDAR_MONTH                              AS CALENDAR_DATE,
                </when>
                <when test="report3ViewType == 'VIEW_BY_QUARTER'.toString()">
                    T.CALENDAR_QUARTER                            AS CALENDAR_DATE,
                </when>
                <when test="report3ViewType == 'VIEW_BY_YEAR'.toString()">
                    T.CALENDAR_YEAR                               AS CALENDAR_DATE,
                </when>
            </choose>
            NVL(T.NON_ABLA_VALUE, 0) AS TOTAL
            FROM NON_ABLA T
        ) mm
        PIVOT (
            SUM(TOTAL) AS TOTAL
            FOR CALENDAR_DATE
            IN (
                <foreach collection="listWithoutDuplicates" separator="," item="item">
                    '${item}'
                </foreach>
            )
        )
        ORDER BY
            <foreach collection="report3SelectedColumns" item="item" separator=",">
                DECODE(
                    ${item},
                    '0D', 'A',
                    '0-1D', 'B',
                    '1-2D', 'C',
                    '2-3D', 'D',
                    '3-7D', 'E',
                    '5-7D', 'F',
                    '3-4D', 'G',
                    '0-1W', 'H',
                    '1-2W', 'I',
                    '2-4W', 'J',
                    '1-2M', 'K',
                    '2-3M', 'L',
                    '3-6M', 'M',
                    '6-12M', 'N',
                    '6-1Y', 'O',
                    '1-2Y', 'P',
                    '>2Y', 'Q',
                    'Others', 'R',
                    ${item})
            </foreach>
    </sql>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3Columns" resultType="java.lang.String">
        SELECT DISTINCT * FROM
        (SELECT
        <choose>
            <when test='report3ViewType == null or report3ViewType == "VIEW_BY_DAY".toString()'>
                distinct T.TEXT as result
            </when>
            <when test='report3ViewType == null or report3ViewType == "VIEW_BY_WEEK".toString()'>
                distinct (T.YEAR || T.WEEK_NO) as result
            </when>
            <when test='report3ViewType == null or report3ViewType == "VIEW_BY_MONTH".toString()'>
                distinct (T.YEAR || T.MONTH) as result
            </when>
            <when test='report3ViewType == null or report3ViewType == "VIEW_BY_QUARTER".toString()'>
                distinct TO_CHAR(T.DATE$, 'yyyy"Q"q') as result
            </when>
            <when test='report3ViewType == null or report3ViewType == "VIEW_BY_YEAR".toString()'>
                distinct T.YEAR as result
            </when>
        </choose>
        FROM SY_CALENDAR t
        WHERE t.DATE$ BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        AND T.NAME = 'National Holidays'
        ORDER BY t.DATE$ DESC
        )
        order by result desc
        OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
    </select>

    <sql id="report3DetailsSQL">
        SELECT /*+ parallel(t 6)*/ * FROM ${SCPA.PMS_STRUCTURE_V} T
        <where>
            <if test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
                <include refid="reportDateFilter">
                    <property name="selectedDate" value="report3SelectedDate"/>
                    <property name="dateRange" value="PMS_CREATED_DATE"/>
                    <property name="viewByDay" value="CALENDAR_DATE"/>
                    <property name="viewByWeek" value="CALENDAR_WEEK"/>
                    <property name="viewByMonth" value="CALENDAR_MONTH"/>
                    <property name="viewByQuarter" value="CALENDAR_QUARTER"/>
                    <property name="viewByYear" value="CALENDAR_YEAR"/>
                    <property name="viewType" value="report3ViewType"/>
                </include>
            </if>
            <include refid="pmsFilter"/>
            <foreach collection="report3SelectedColumns" item="item" index="index">
                <if test="report3SelectedValues[index] != null and report3SelectedValues[index] != ''.toString()">
                    and t.${item} = #{report3SelectedValues[${index}], jdbcType=VARCHAR}
                </if>
            </foreach>
        </where>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

</mapper>
