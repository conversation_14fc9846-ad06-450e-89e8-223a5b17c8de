package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.inventory.bean.PositiveNagitiveList;
import com.scp.inventory.bean.Report1DataBean;
import com.scp.inventory.bean.StructureTreemap;
import com.scp.inventory.dao.IInventoryStructureDao;
import com.scp.inventory.service.IInventoryStructureService;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.ColorUtils;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("inventoryStructureService")
@Scope("prototype")
@Transactional
public class InventoryStructureServiceImpl extends ServiceHelper implements IInventoryStructureService {

    @Resource
    private IInventoryStructureDao inventoryStructureDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", this.parseCascader(inventoryStructureDao.queryCascader()));
        resultMap.put("projectionVersionOpts", inventoryStructureDao.queryProjectionVersionOpts());
        resultMap.put("priceReferenceOpts", inventoryStructureDao.queryPriceReferenceOpts());

        return response.setBody(resultMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response clearCache() {
        return response;
    }

    // region report1
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        String positiveColor = (String) parameterMap.get("positiveColor");
        String middleColor = (String) parameterMap.get("middleColor");
        String nagitiveColor = (String) parameterMap.get("nagitiveColor");


        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnNameByLabel(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnNameByLabel(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnNameByLabel(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnNameByLabel(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnNameByLabel(parameterMap.get("level5")));

        List<StructureTreemap> resultList = new ArrayList<>();
        List<Report1DataBean> dataList;

        // 大部分情况下, 用户只看当天的, 如果这时候也用HIST表来查询, 就会很慢, 所以加了这个判断
        if (new SimpleDateFormat("yyyy/MM/dd").format(new Date()).equals(parameterMap.get("report1DateRange"))) {
            dataList = inventoryStructureDao.queryReport1Today(parameterMap);
        } else {
            dataList = inventoryStructureDao.queryReport1(parameterMap);
        }

        for (Report1DataBean data : dataList) {
            this.convertReport1Data(resultList, data);
        }

        // 第一层的趋势使用精确计算
        List<Map<String, Object>> level1Slope = inventoryStructureDao.queryReport1Level1Slope(parameterMap);
        Map<String, Object> level1SlopeMap = new HashMap<>();
        List<BigDecimal> positiveList = new ArrayList<>();
        List<BigDecimal> nagitiveList = new ArrayList<>();
        for (Map<String, Object> map : level1Slope) {
            BigDecimal k = Utils.parseBigDecimal(map.get("K"));
            level1SlopeMap.put((String) map.get("KEY"), k);
            if (k.compareTo(BigDecimal.ZERO) >= 0) {
                if (positiveList.contains(k) == false) {
                    positiveList.add(k);
                }
            } else {
                if (nagitiveList.contains(k) == false) {
                    nagitiveList.add(k);
                }
            }
        }
        positiveList.sort(Comparator.naturalOrder());
        nagitiveList.sort(Comparator.reverseOrder());

        for (StructureTreemap level1 : resultList) {
            BigDecimal k = Utils.parseBigDecimal(level1SlopeMap.get(level1.getName()));
            level1.setK(k.doubleValue());
            level1.setColor(this.getRangeColor(positiveList, nagitiveList, k, positiveColor, middleColor, nagitiveColor));
        }

        // 第二层的趋势使用精确计算
        List<Map<String, Object>> level2Slope = inventoryStructureDao.queryReport1Level2Slope(parameterMap);
        Map<String, Object> level2SlopeMap = new HashMap<>();

        for (Map<String, Object> map : level2Slope) {
            BigDecimal k = Utils.parseBigDecimal(map.get("K"));
            level2SlopeMap.put((String) map.get("KEY"), k);
        }

        for (StructureTreemap level1 : resultList) {
            positiveList = new ArrayList<>();
            nagitiveList = new ArrayList<>();
            for (StructureTreemap level2 : level1.getChildren()) {
                BigDecimal k = Utils.parseBigDecimal(level2SlopeMap.get(level1.getName() + "[#]" + level2.getName()));
                if (k.compareTo(BigDecimal.ZERO) >= 0) {
                    if (positiveList.contains(k) == false) {
                        positiveList.add(k);
                    }
                } else {
                    if (nagitiveList.contains(k) == false) {
                        nagitiveList.add(k);
                    }
                }
            }
            positiveList.sort(Comparator.naturalOrder());
            nagitiveList.sort(Comparator.reverseOrder());
            for (StructureTreemap level2 : level1.getChildren()) {
                BigDecimal k = Utils.parseBigDecimal(level2SlopeMap.get(level1.getName() + "[#]" + level2.getName()));
                level2.setK(k.doubleValue());
                level2.setColor(this.getRangeColor(positiveList, nagitiveList, k, positiveColor, middleColor, nagitiveColor));
            }
        }

        // 3, 4, 5层使用模糊计算
        PositiveNagitiveList positiveNagitiveList;
        for (StructureTreemap level1 : resultList) {
            for (StructureTreemap level2 : level1.getChildren()) {
                // 为Level3染色
                positiveNagitiveList = this.getPositiveNagitiveList(level2.getChildren());
                for (StructureTreemap level3 : level2.getChildren()) {
                    level3.setColor(this.getRangeColor(positiveNagitiveList.getPositiveList(), positiveNagitiveList.getNagitiveList(), BigDecimal.valueOf(level3.getK()), positiveColor, middleColor, nagitiveColor));
                }

                // 继续向下遍历
                for (StructureTreemap level3 : level2.getChildren()) {
                    // 为Level4染色
                    positiveNagitiveList = this.getPositiveNagitiveList(level3.getChildren());
                    for (StructureTreemap level4 : level3.getChildren()) {
                        level4.setColor(this.getRangeColor(positiveNagitiveList.getPositiveList(), positiveNagitiveList.getNagitiveList(), BigDecimal.valueOf(level4.getK()), positiveColor, middleColor, nagitiveColor));
                    }

                    // 继续向下遍历
                    for (StructureTreemap level4 : level3.getChildren()) {
                        // 为Level5染色
                        positiveNagitiveList = this.getPositiveNagitiveList(level4.getChildren());
                        for (StructureTreemap level5 : level4.getChildren()) {
                            level5.setColor(this.getRangeColor(positiveNagitiveList.getPositiveList(), positiveNagitiveList.getNagitiveList(), BigDecimal.valueOf(level5.getK()), positiveColor, middleColor, nagitiveColor));
                        }
                    }
                }
            }
        }

        return response.setBody(resultList);
    }

    private PositiveNagitiveList getPositiveNagitiveList(List<StructureTreemap> tree) {
        PositiveNagitiveList positiveNagitiveList = new PositiveNagitiveList();
        for (StructureTreemap level : tree) {
            BigDecimal k = BigDecimal.valueOf(level.getK());
            if (k.compareTo(BigDecimal.ZERO) >= 0) {
                if (positiveNagitiveList.getPositiveList().contains(k) == false) {
                    positiveNagitiveList.getPositiveList().add(k);
                }
            } else {
                if (positiveNagitiveList.getNagitiveList().contains(k) == false) {
                    positiveNagitiveList.getNagitiveList().add(k);
                }
            }
        }
        positiveNagitiveList.getPositiveList().sort(Comparator.naturalOrder());
        positiveNagitiveList.getNagitiveList().sort(Comparator.reverseOrder());
        return positiveNagitiveList;
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<StructureTreemap> list, Report1DataBean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categorys = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categorys.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<StructureTreemap> child = new ArrayList<>();
        StructureTreemap root = new StructureTreemap();
        root.setName(categorys.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categorys.size() - 1; i++) {
            StructureTreemap treemap = new StructureTreemap();
            treemap.setName(categorys.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        StructureTreemap lastNode = new StructureTreemap();
        lastNode.setName(categorys.get(categorys.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<StructureTreemap> beanOpt = list.stream().filter(b -> b.getName().equals(categorys.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            StructureTreemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    List<String> positiveColorList = new ArrayList<>();
    List<String> nagitiveColorList = new ArrayList<>();

    private String getRangeColor(List<BigDecimal> positiveList, List<BigDecimal> nagitiveList, BigDecimal k, String positiveColor, String middleColor, String nagitiveColor) {
        if (positiveColorList.size() != positiveList.size()) {
            positiveColorList = ColorUtils.gradient(middleColor, positiveColor, positiveList.size());
        }

        if (nagitiveColorList.size() != nagitiveList.size()) {
            nagitiveColorList = ColorUtils.gradient(middleColor, nagitiveColor, nagitiveList.size());
        }

        if (positiveList.contains(k)) {
            return positiveColorList.get(positiveList.lastIndexOf(k));
        } else if (nagitiveList.contains(k)) {
            return nagitiveColorList.get(nagitiveList.lastIndexOf(k));
        }
        return "#000000";
    }

    // endregion

    // region report2
    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        List<String> legends = this.generateReport2StackedVariables(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String resultType = (String) parameterMap.get("resultType");
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> dataList;
        if ("Accounting View".equalsIgnoreCase(resultType)) {
            dataList = inventoryStructureDao.queryReport2WithPriceReferenceAccountingView(parameterMap);
        } else if (StringUtils.isNotBlank((String) parameterMap.get("priceReference"))) {
            dataList = inventoryStructureDao.queryReport2WithPriceReference(parameterMap);
        } else {
            dataList = inventoryStructureDao.queryReport2(parameterMap);
        }

        List<String> xAxis = new ArrayList<>();
        List<BigDecimal> yAxis = new ArrayList<>();
        List<BigDecimal> aAxis = new ArrayList<>(); // amu坐标
        List<BigDecimal> cAxis = new ArrayList<>(); // cover坐标
        List<BigDecimal> prAxis = new ArrayList<>(); // price reference

        for (Map<String, Object> map : dataList) {
            xAxis.add((String) map.get("xAxis"));
            BigDecimal value = Utils.parseBigDecimal(map.get("yAxis"));
            BigDecimal amu = Utils.parseBigDecimal(map.get("aAxis"));
            BigDecimal prValue = Utils.parseBigDecimal(map.get("prAxis"));
            BigDecimal cover;
            yAxis.add(value);
            aAxis.add(amu);
            prAxis.add(prValue);
            if (amu.compareTo(BigDecimal.ZERO) == 0) {
                cover = BigDecimal.ZERO;
            } else {
                cover = value.divide(amu, 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(30)).setScale(1, RoundingMode.HALF_UP);
            }
            cAxis.add(cover);
        }

        for (int i = 0; i < legends.size(); i++) {
            List<BigDecimal> y = new ArrayList<>();
            for (Map<String, Object> map : dataList) {
                y.add(Utils.parseBigDecimal(map.get("yAxis" + (i + 2))));
            }
            resultMap.put("yAxis" + (i + 2), y);
        }

        // projection xAxis
        List<String> dateRange = (List<String>) parameterMap.get("dateRange");
        String start = StringUtils.remove(dateRange.get(0), "/");
        String end = StringUtils.remove(dateRange.get(1), "/");
        String projectionVersion = (String) parameterMap.get("projectionVersion");

        // 判断用户是否选择了"BLOCKED_STOCK", "RESTRICTED_STOCK", "RETURNS_STOCK", "STOCK_IN_QI", "UU_STOCK"
        boolean containAllValue = true;
        String[] all = {"BLOCKED_STOCK", "RESTRICTED_STOCK", "RETURNS_STOCK", "STOCK_IN_QI", "UU_STOCK"};
        List<String> stockTypes = Utils.object2StrList(parameterMap.get("stock"));
        for (String s : all) {
            if (stockTypes.contains(s) == false) {
                containAllValue = false;
                break;
            }
        }

        // 只有version包含在daterange中, 才会显示projection
        if (StringUtils.compare(start, projectionVersion + "01") <= 0 && StringUtils.compare(end, projectionVersion + "01") >= 0 && // projection version必须落在选择的区间内
                "Moving Average Price".equalsIgnoreCase((String) parameterMap.get("resultType")) &&// 必须是金额
                containAllValue == true // 必须包含所有的value列
        ) {
            // projection数据
            Map<String, BigDecimal> projectionData = inventoryStructureDao.queryReport2ProjectionData(parameterMap);

            // 判断projection data所有值是不是一样, 如果所有的值都一样, 说明没有projection, 同样不显示projection的线
            boolean allEqual = true;
            BigDecimal tempValue = null;
            for (int i = 0; i < 14; i++) {
                String key = "MONTH" + (i < 10 ? "0" + i : i);
                if (i == 0) {
                    tempValue = projectionData.getOrDefault(key, BigDecimal.ZERO);
                } else {
                    if (tempValue.compareTo(projectionData.getOrDefault(key, BigDecimal.ZERO)) != 0) {
                        allEqual = false;
                        break;
                    }
                }
            }

            // 如果有projection的数据, 再渲染projection的图形, 否则不渲染
            if (projectionData.isEmpty() == false && allEqual == false) {
                // 生成所有projection坐标
                List<String> projectionAxis = new ArrayList<>();
                projectionAxis.add(projectionVersion.substring(0, 4) + "/" + projectionVersion.substring(4, 6) + "/01");
                for (int i = 1; i <= 13; i++) {
                    projectionAxis.add(this.getLastDayOfMonth(projectionVersion));
                    projectionVersion = Utils.addMonth(projectionVersion);
                }

                List<BigDecimal> pAxis = new ArrayList<>();
                // 将xAxis和projectionAxis合并
                // 先遍历xAxis, 再遍历projectionAxis
                // 遍历xAxis的时候, 如果坐标在projectionAxis中, 则为pAxis赋值, 否则赋值为null
                for (String x : xAxis) {
                    int index = projectionAxis.indexOf(x);
                    if (index == -1) {
                        pAxis.add(null);
                    } else {
                        String key = "MONTH" + (index < 10 ? "0" + index : index);
                        pAxis.add(Utils.parseBigDecimal(projectionData.get(key)));
                    }
                }

                // 再遍历projectionAxis
                for (int i = 0; i < projectionAxis.size(); i++) {
                    String x = projectionAxis.get(i);
                    if (xAxis.contains(x) == false) {
                        String key = "MONTH" + (i < 10 ? "0" + i : i);
                        xAxis.add(x);
                        pAxis.add(Utils.parseBigDecimal(projectionData.get(key)));
                    }
                }

                // 将得到的pAxis数组进行重新计算, 绘制一条斜线
                // 先要获得数组中null值前后的两个数字, 然后将中间的null值逐个填充
                int deadloop = 10;
                while (pAxis.contains(null) && deadloop > 0) {
                    BigDecimal firstValue = null;
                    int firstIndex = -1;
                    BigDecimal secondValue = null;
                    int secondIndex = -1;

                    // 从第一个不为null的值开始遍历
                    int startIndex = 0;
                    for (BigDecimal pAxi : pAxis) {
                        if (pAxi != null) {
                            break;
                        }
                        startIndex++;
                    }

                    for (int i = startIndex; i < pAxis.size(); i++) {
                        BigDecimal value = pAxis.get(i);

                        if (firstIndex == -1 && value == null) {
                            if (i == 0) {
                                continue;
                            }
                            firstIndex = i - 1;
                            firstValue = pAxis.get(firstIndex);
                            firstValue = (firstValue == null) ? BigDecimal.ZERO : firstValue;
                        }

                        if (firstIndex != -1 && value != null && secondIndex == -1) {
                            secondIndex = i;
                            secondValue = pAxis.get(secondIndex);
                            secondValue = (secondValue == null) ? BigDecimal.ZERO : secondValue;
                        }
                    }
                    // 计算step
                    int gap = secondIndex - firstIndex - 1;
                    BigDecimal stepValue = BigDecimal.ZERO;
                    if (gap > 0 && secondValue != null) {
                        stepValue = secondValue.subtract(firstValue).divide(BigDecimal.valueOf(gap), 2, RoundingMode.HALF_UP);
                    }

                    // 最后将pAxis中间为null的部分补上值
                    for (int i = firstIndex + 1; i < secondIndex; i++) {
                        firstValue = firstValue.add(stepValue);
                        pAxis.set(i, firstValue);
                    }
                    deadloop--;
                }
                resultMap.put("pAxis", pAxis);
            }
        }

        resultMap.put("xAxis", xAxis);
        resultMap.put("yAxis", yAxis);
        resultMap.put("aAxis", aAxis);
        resultMap.put("cAxis", cAxis);
        if (StringUtils.isNotBlank((String) parameterMap.get("priceReference"))) {
            resultMap.put("prAxis", prAxis);
        }

        resultMap.put("legends", legends);
        return response.setBody(resultMap);
    }

    private String getLastDayOfMonth(String version) {
        int year = Utils.parseInt(version.substring(0, 4));
        int month = Utils.parseInt(version.substring(4, 6));
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return new SimpleDateFormat("yyyy/MM/dd").format(calendar.getTime());
    }

    // endregion

    // region report3
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(inventoryStructureDao.queryReport3Columns(parameterMap));
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateReport3ValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();

        Object columnsObj = parameterMap.get("report3ColumnNames");
        List<String> selectedColumn = (List<String>) parameterMap.get("report3SelectedColumn");
        if (columnsObj instanceof JSONArray columns && ((JSONArray) columnsObj).isEmpty() == false) {
            dataList = inventoryStructureDao.queryReport3(parameterMap);

            LinkedHashMap<String, Object> totalMap = new LinkedHashMap<>();
            totalMap.put(selectedColumn.get(0), "Total");
            for (Object o : columns) {
                BigDecimal total = BigDecimal.ZERO;
                String key = "'" + o + "'_TOTAL";
                for (LinkedHashMap<String, Object> map : dataList) {
                    total = total.add(Utils.parseBigDecimal(map.get(key)));
                }
                totalMap.put(key, total);
            }
            dataList.add(totalMap);
        } else {
            LinkedHashMap<String, Object> temp = new LinkedHashMap<>();
            JSONArray dateRange = (JSONArray) parameterMap.get("dateRange");
            temp.put(selectedColumn.get(0), "在" + dateRange.get(0) + "~" + dateRange.get(1) + "内未发现数据, 请选择包含1号或15号的时间范围");
            dataList.add(temp);
        }

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }


    @Override
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport3ValueColumn(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "inventory_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryStructureDao.queryReport3", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport3ValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(inventoryStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "inventory_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryStructureDao.queryReport3Details", parameterMap);
    }

    //endregion

    //region report4
    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap, "INVENTORY_MONITOR_TYPE_SNAPSHOT");
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport4ValueColumn(parameterMap);
        String type = (String) parameterMap.get("report4ConvType");
        if ("AMF".equalsIgnoreCase(type)) {
            parameterMap.put("convColumn", "AMF_ONE_MM_MODIFIED");
        } else if ("MAX(AMU, AMF)".equalsIgnoreCase(type)) {
            parameterMap.put("convColumn", "GREATEST(AMF_ONE_MM_MODIFIED, AMU_ONE_MM_MODIFIED)");
        } else {
            parameterMap.put("convColumn", "AMU_ONE_MM_MODIFIED");
        }
        List<String> selectedColumn = (List<String>) parameterMap.get("report4SelectedColumn");

        List<LinkedHashMap<String, Object>> resultList = inventoryStructureDao.queryReport4(parameterMap);
        if (resultList.isEmpty() == false) {
            // 计算最后一行的Total
            Map<String, Object> colMap = resultList.get(0);
            List<String> colNames = new ArrayList<>(colMap.keySet());
            LinkedHashMap<String, Object> totalBottom = new LinkedHashMap<>();
            for (int i = 0; i < colNames.size(); i++) {
                String colName = colNames.get(i);
                if (i == 0) {
                    totalBottom.put(colName, "Total");
                    continue;
                }
                // 不统计分组列的和
                if (i < selectedColumn.size()) {
                    continue;
                }
                BigDecimal total = BigDecimal.ZERO;
                for (Map<String, Object> map : resultList) {
                    total = total.add(Utils.parseBigDecimal(map.get(colName), BigDecimal.ZERO));
                }
                totalBottom.put(colName, total.compareTo(BigDecimal.ZERO) == 0 ? null : total);
            }
            resultList.add(totalBottom);

            // 计算每行的Total
            for (Map<String, Object> map : resultList) {
                BigDecimal total = BigDecimal.ZERO;
                for (int i = Math.min(selectedColumn.size(), colNames.size()); i < colNames.size(); i++) {
                    total = total.add(Utils.parseBigDecimal(map.get(colNames.get(i)), BigDecimal.ZERO));
                }
                map.put("Total", total.compareTo(BigDecimal.ZERO) == 0 ? null : total);
            }

            // 根据每行的Total计算百分比
            for (Map<String, Object> map : resultList) {
                BigDecimal total = Utils.parseBigDecimal(map.get("Total"));
                for (int i = 1; i < colNames.size(); i++) {
                    if (total.compareTo(BigDecimal.ZERO) == 0) {
                        map.put(colNames.get(i) + "_P", null);
                    } else {
                        if (map.get(colNames.get(i)) == null) {
                            map.put(colNames.get(i) + "_P", null);
                        } else {
                            map.put(colNames.get(i) + "_P", Utils.parseBigDecimal(map.get(colNames.get(i)), BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).divide(total, 1, RoundingMode.HALF_UP));
                        }
                    }
                }
            }

            // 计算每行Total的占比
            BigDecimal resultTotal = Utils.parseBigDecimal(totalBottom.get("Total"), BigDecimal.ZERO);
            for (Map<String, Object> map : resultList) {
                if (resultTotal.compareTo(BigDecimal.ZERO) == 0) {
                    map.put("Total_P", null);
                } else {
                    map.put("Total_P", Utils.parseBigDecimal(map.get("Total"), BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).divide(resultTotal, 1, RoundingMode.HALF_UP));
                }
            }
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap, "INVENTORY_MONITOR_TYPE_SNAPSHOT");
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport4ValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        String type = (String) parameterMap.get("report4ConvType");
        if ("AMF".equalsIgnoreCase(type)) {
            parameterMap.put("convColumn", "AMF_ONE_MM_MODIFIED");
        } else if ("MAX(AMU, AMF)".equalsIgnoreCase(type)) {
            parameterMap.put("convColumn", "GREATEST(AMF_ONE_MM_MODIFIED, AMU_ONE_MM_MODIFIED)");
        } else {
            parameterMap.put("convColumn", "AMU_ONE_MM_MODIFIED");
        }

        page.setTotal(inventoryStructureDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryStructureDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap, "INVENTORY_MONITOR_TYPE_SNAPSHOT");
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport4ValueColumn(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String type = (String) parameterMap.get("report4ConvType");
        if ("AMF".equalsIgnoreCase(type)) {
            parameterMap.put("convColumn", "AMF_ONE_MM_MODIFIED");
        } else if ("MAX(AMU, AMF)".equalsIgnoreCase(type)) {
            parameterMap.put("convColumn", "GREATEST(AMF_ONE_MM_MODIFIED, AMU_ONE_MM_MODIFIED)");
        } else {
            parameterMap.put("convColumn", "AMU_ONE_MM_MODIFIED");
        }

        String fileName = "inventory_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryStructureDao.queryReport4Details", parameterMap);
    }
    //endregion

    //region private functions
    private void generateFilter(Map<String, Object> parameterMap, String... excludeKeys) {
        // Date
        List<String> dateRange = (List<String>) parameterMap.get("dateRange");
        parameterMap.put("report1DateRange", dateRange.get(1));

        // 判断是否需要使用HIST_V
        parameterMap.put("enable_inventory_structure_hist_v", this.enableInventoryStructureHistV(parameterMap));

        // 生成筛选条件
        List<String> stockIndicator = new ArrayList<>();
        this.generateCascaderFilterSQL(parameterMap, value -> {
            if ("SPECIAL_ST".equalsIgnoreCase(value.getKey())) {
                stockIndicator.add(value.getValue());
                return false;
            }
            return true;
        }, null, "T", "_filters", excludeKeys);

        // stock indicator
        if (stockIndicator.isEmpty() == false) {
            String sql = "";
            String sql2 = "";
            if (stockIndicator.contains("#")) {
                stockIndicator.remove("#");
                sql2 = "t.SPECIAL_ST is null";
            }
            if (stockIndicator.isEmpty() == false) {
                List<String> indicators = new ArrayList<>();

                for (String value : stockIndicator) {
                    String key = Utils.randomStr(8);
                    indicators.add("#{" + key + ",jdbcType=VARCHAR}");
                    parameterMap.put(key, value);
                }


                sql = "t.SPECIAL_ST in (" + StringUtils.join(indicators, ",") + ")";
            }

            parameterMap.put("stockIndicatorSQL", "(" + sql + (StringUtils.isNotBlank(sql) && StringUtils.isNotBlank(sql2) ? " or " : "") + sql2 + ")");
        }
    }

    private boolean enableInventoryStructureHistV(Map<String, Object> parameterMap) {
        String[] targets = new String[]{"INVENTORY_MONITOR_TYPE", "MATERIAL_OWNER_"};
        for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
            if (StringUtils.equals(entry.getKey(), "SCPA") || StringUtils.equals(entry.getKey(), "session")) {
                continue;
            }
            // 检查key是否包含目标字符串
            for (String target : targets) {
                if (StringUtils.containsIgnoreCase(entry.getKey(), target)) {
                    return true;
                }
            }

            // 检查value是否包含目标字符串，这里假设value是String类型
            String valueStr = JSONObject.toJSONString(entry.getValue());
            for (String target : targets) {
                if (StringUtils.containsIgnoreCase(valueStr, target)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnNameByLabel(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnNameByLabel(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("tooltips")).toJavaList(String.class);
        if (tooltips.isEmpty() == false) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnNameByLabel).collect(Collectors.toList());

            List<String> tooltipsColumnsName = new ArrayList<>();

            for (String c : tooltipsColumns) {
                if (StringUtils.contains(c, "WIP_") || StringUtils.contains(c, "GIT_")) {
                    tooltipsColumnsName.add("nvl(sum(" + c + "),0) AS " + c);
                } else {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        if (c.equalsIgnoreCase("PROPOSED_FIN_PROV")) {
                            tooltipsColumnsName.add("nvl(sum(" + c + "_QTY),0) AS " + c);
                        } else {
                            tooltipsColumnsName.add("nvl(sum(" + c + "),0) AS " + c);
                        }
                    } else if ("Moving Average Price".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("nvl(sum(" + c + "_VALUE),0) AS " + c);
                    } else if ("Pallet".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("nvl(sum(case nvl(PALLETIZATION_QTY, 0) when 0 then 0 else " + c + " / PALLETIZATION_QTY end),0)  AS " + c);
                    }
                }
            }

            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private List<String> generateReport2StackedVariables(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("stackedVariables")).toJavaList(String.class);

        List<String> stackedVariablesNames = new ArrayList<>();
        List<String> stackedVariablesColumns = new ArrayList<>();
        if (tooltips.isEmpty() == false) {
            stackedVariablesNames = tooltips.stream().map(this::getColumnNameByLabel).collect(Collectors.toList());

            for (int i = 0; i < stackedVariablesNames.size(); i++) {
                String c = stackedVariablesNames.get(i);
                if (StringUtils.contains(c, "WIP_") || StringUtils.contains(c, "GIT_")) {
                    stackedVariablesColumns.add("nvl(sum(" + c + "),0) as \"yAxis" + (i + 2) + "\"");
                } else {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        if (c.equalsIgnoreCase("PROPOSED_FIN_PROV")) {
                            stackedVariablesColumns.add("nvl(sum(" + c + "_QTY),0) as \"yAxis" + (i + 2) + "\"");
                        } else {
                            stackedVariablesColumns.add("nvl(sum(" + c + "),0) as \"yAxis" + (i + 2) + "\"");
                        }

                    } else if ("Moving Average Price".equalsIgnoreCase(resultType)) {
                        stackedVariablesColumns.add("nvl(sum(" + c + "_VALUE),0) as \"yAxis" + (i + 2) + "\"");
                    } else if ("Pallet".equalsIgnoreCase(resultType)) {
                        stackedVariablesColumns.add("nvl(sum(case nvl(PALLETIZATION_QTY, 0) when 0 then 0 else " + c + " / PALLETIZATION_QTY end),0) as \"yAxis" + (i + 2) + "\"");
                    }
                }
            }
        }
        parameterMap.put("stackedVariablesColumns", stackedVariablesColumns);
        return stackedVariablesNames;
    }

    @SuppressWarnings("unchecked")
    private void generateReport3ValueColumn(Map<String, Object> parameterMap) {
        JSONArray selectedColumn = (JSONArray) parameterMap.get("report3SelectedColumn");
        if (selectedColumn.isEmpty()) {
            selectedColumn.add("CLUSTER_NAME");
            selectedColumn.add("ENTITY");
            parameterMap.put("report3SelectedColumn", selectedColumn);
        }

        List<String> report3SelectedValue = (List<String>) parameterMap.get("report3SelectedValue");
        List<String> report3SelectedValueNew = new ArrayList<>();
        for (int i = 0; i < selectedColumn.size(); i++) {
            String firstValue = "";
            if (report3SelectedValue.isEmpty() == false) {
                firstValue = report3SelectedValue.get(0);
            }

            if ("Total".equals(firstValue)) {
                report3SelectedValueNew.add("Total");
            } else {
                String value = "";
                if (report3SelectedValue.size() > i) {
                    value = report3SelectedValue.get(i);
                }
                report3SelectedValueNew.add(value);
            }
        }
        parameterMap.put("report3SelectedValue", report3SelectedValueNew);
    }

    private void generateReport4ValueColumn(Map<String, Object> parameterMap) {
        JSONArray selectedColumn = (JSONArray) parameterMap.get("report4SelectedColumn");
        if (selectedColumn.isEmpty()) {
            selectedColumn.add("LT_GROUP");
            parameterMap.put("report4SelectedColumn", selectedColumn);
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> stockTypes = Utils.object2StrList(parameterMap.get("stock"));
        List<String> columns = stockTypes.stream().map(this::getColumnNameByLabel).collect(Collectors.toList());
        if (columns.isEmpty()) {
            columns.add("UU_STOCK");
        }

        String priceReferenceValueColumn = "";
        String amuValueColumn = "";
        String valueColumn = "";
        String slopeType = "QTY_";
        String qtyColumn = " nvl( " + StringUtils.join(columns, ",0) + nvl( ") + ",0)";
        qtyColumn = qtyColumn.replace(" GIT", "GIT_QTY");
        qtyColumn = qtyColumn.replace(" WIP", "WIP_QTY");
        if ("Quantity".equalsIgnoreCase(resultType)) {
            valueColumn = " nvl( " + StringUtils.join(columns, ",0) + nvl( ") + ",0)";
            valueColumn = valueColumn.replace(" GIT", "GIT_QTY");
            valueColumn = valueColumn.replace(" WIP", "WIP_QTY");
            amuValueColumn = "AMU_ONE_MM";
            priceReferenceValueColumn = qtyColumn;
        } else if ("Moving Average Price".equalsIgnoreCase(resultType)) {
            slopeType = "VALUE_";
            String sql = "";
            List<String> valueColumns = new ArrayList<>();
            List<String> prValueColumns = new ArrayList<>();
            for (String c : columns) {
                valueColumns.add(c + "_VALUE");
                prValueColumns.add("nvl(case when t.DATE$ >= t2.DATE$ then " + "least(" + c + " * t2." + c + "_PRICE, " + c + "_VALUE) else " + "greatest(" + c + " * t2." + c + "_PRICE, " + c + "_VALUE) end, " + c + "_VALUE)");
            }
            valueColumn = " nvl( " + StringUtils.join(valueColumns, ",0) + nvl( ") + ",0)";
            valueColumn += sql;
            amuValueColumn = "AMU_ONE_MM_VALUE";
            priceReferenceValueColumn = " nvl( " + StringUtils.join(prValueColumns, ",0) + nvl( ") + ",0)";
            priceReferenceValueColumn = priceReferenceValueColumn.replace("GIT *", "GIT_QTY *");
            priceReferenceValueColumn = priceReferenceValueColumn.replace("WIP *", "WIP_QTY *");
        } else if ("Pallet".equalsIgnoreCase(resultType)) {
            valueColumn = "( nvl(" + StringUtils.join(columns, ",0) + nvl( ") + ",0))";
            valueColumn = valueColumn.replace(" GIT", "GIT_QTY");
            valueColumn = valueColumn.replace(" WIP", "WIP_QTY");
            valueColumn = "case nvl(PALLETIZATION_QTY, 0) when 0 then 0 else " + valueColumn + " / PALLETIZATION_QTY end";
            amuValueColumn = "0";
            priceReferenceValueColumn = qtyColumn;
        } else if ("Accounting View".equalsIgnoreCase(resultType)) {
            slopeType = "VALUE_";
            String sql = "";
            List<String> valueColumns = new ArrayList<>();
            List<String> prValueColumns = new ArrayList<>();
            for (String c : columns) {
                if (StringUtils.equals(c, "GIT")) {
                    valueColumns.add("GIT_VALUE");
                    StringBuilder gitValue = new StringBuilder();
                    gitValue.append(" CASE WHEN T.DATE$ >= T2.DATE$ THEN LEAST(NVL(GIT_QTY * T2.GIT_PRICE, 0), NVL(GIT_VALUE, 0)) ");
                    gitValue.append(" ELSE GREATEST(NVL(GIT_QTY * T2.GIT_PRICE, 0), NVL(GIT_VALUE, 0)) END ");
                    prValueColumns.add(gitValue.toString());
                } else if (StringUtils.equals(c, "WIP")) {
                    valueColumns.add("WIP_VALUE");
                    StringBuilder wipValue = new StringBuilder();
                    wipValue.append(" CASE WHEN T.DATE$ >= T2.DATE$ THEN LEAST(NVL(WIP_QTY * T2.WIP_PRICE, 0), NVL(WIP_VALUE, 0)) ");
                    wipValue.append(" ELSE GREATEST(NVL(WIP_QTY * T2.WIP_PRICE, 0), NVL(WIP_VALUE, 0)) END ");
                    prValueColumns.add(wipValue.toString());
                } else {
                    valueColumns.add("CASE WHEN T.MOVING_AVERAGE_P IS NULL OR T.MOVING_AVERAGE_P = 0 THEN " + c + "_VALUE ELSE " + c + " *  DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, T.MOVING_AVERAGE_P / T.PRICE_UNIT) END");
                    StringBuilder prValue = new StringBuilder();
                    prValue.append(" CASE WHEN ").append(c).append("_PRICE IS NULL THEN ").append(c).append(" * DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, T.MOVING_AVERAGE_P / T.PRICE_UNIT) ");
                    prValue.append(" ELSE ");
                    prValue.append(" CASE WHEN T.MOVING_AVERAGE_P IS NULL OR T.MOVING_AVERAGE_P = 0 ");
                    prValue.append(" THEN ").append(c).append("_VALUE");
                    prValue.append(" WHEN T.DATE$ >= T2.DATE$ THEN ");
                    prValue.append(" LEAST(NVL(").append(c).append(" * T2.").append(c).append("_PRICE, 0), NVL(").append(c).append(" * DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, T.MOVING_AVERAGE_P / T.PRICE_UNIT), 0)) ELSE ");
                    prValue.append(" GREATEST(NVL(").append(c).append(" * T2.").append(c).append("_PRICE, 0), NVL(").append(c).append(" * DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, T.MOVING_AVERAGE_P / T.PRICE_UNIT), 0) ) END");
                    prValue.append(" END ");
                    prValueColumns.add(prValue.toString());
                }

            }
            valueColumn = " NVL( " + StringUtils.join(valueColumns, ",0) + NVL( ") + ",0)";
            valueColumn += sql;
            amuValueColumn = "AMU_ONE_MM_VALUE";
            priceReferenceValueColumn = " nvl( " + StringUtils.join(prValueColumns, ",0) + nvl( ") + ",0)";
        } else if ("Avg Selling Price".equalsIgnoreCase(resultType)) {
            List<String> valueColumns = new ArrayList<>();
            List<String> prValueColumns = new ArrayList<>();
            for (String c : columns) {
                if (StringUtils.equals(c, "GIT")) {
                    valueColumns.add("NVL(GIT_VALUE, 0)");
                } else if (StringUtils.equals(c, "WIP")) {
                    valueColumns.add("NVL(WIP_VALUE, 0)");
                } else {
                    valueColumns.add("DECODE(NVL(DECODE(T.MATERIAL_TYPE,'Finish Goods', T.AVG_SELLING_PRICE_RMB, T.UNIT_COST), 0), 0, nvl(" + c + "_VALUE, 0), nvl(" + c + ", 0) * DECODE(T.MATERIAL_TYPE,'Finish Goods', T.AVG_SELLING_PRICE_RMB, T.UNIT_COST))");
                }
                prValueColumns.add("nvl(case when t.DATE$ >= t2.DATE$ then " + "least(" + c + " * t2." + c + "_PRICE, " + c + "_VALUE) else " + "greatest(" + c + " * t2." + c + "_PRICE, " + c + "_VALUE) end, " + c + "_VALUE)");
            }
            valueColumn = StringUtils.join(valueColumns, " + ");
            amuValueColumn = "AMU_ONE_MM_VALUE";
            priceReferenceValueColumn = " nvl( " + StringUtils.join(prValueColumns, ",0) + nvl( ") + ",0)";
            priceReferenceValueColumn = priceReferenceValueColumn.replace("GIT *", "GIT_QTY *");
            priceReferenceValueColumn = priceReferenceValueColumn.replace("WIP *", "WIP_QTY *");
        }

        parameterMap.put("slopeType", slopeType);
        parameterMap.put("qtyColumn", qtyColumn.trim());
        parameterMap.put("valueColumn", valueColumn.trim());
        parameterMap.put("priceReferenceValueColumn", priceReferenceValueColumn.trim());
        parameterMap.put("amuValueColumn", amuValueColumn.trim());
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if ("MATERIAL_PLANT".equalsIgnoreCase(label)) {
            return "MATERIAL || ' ' || T.PLANT_CODE";
        }
        switch (label) {
            case "UD_<7CD" -> {
                return "UD_LT_7_CD";
            }
            case "UD_<30CD" -> {
                return "UD_LT_30_CD";
            }
            case "UD_>30CD" -> {
                return "UD_GT_30_CD";
            }
            default -> {
                if (Utils.hasInjectionAttack(label) == false) {
                    return label;
                } else {
                    return "";
                }
            }
        }
    }

    //endregion
}
