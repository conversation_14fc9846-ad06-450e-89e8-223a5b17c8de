<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IDistributorInventoryDao">
    <select id="queryCascader" resultType="java.util.Map">
        select * from DISTRIBUTOR_INVENTORY_FILTER_V order by category,decode (name,'Others','zzz',name)
    </select>

    <sql id="queryReport1Sql">
        <choose>
            <when test="sort =='Top 50 Inventory'.toString()">
                WITH BASE AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(INVENTORY_VALUE) DESC FETCH FIRST 50 ROWS ONLY)
            </when>
            <when test="sort =='Top 30 Inventory'.toString()">
                WITH BASE AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(INVENTORY_VALUE) DESC FETCH FIRST 30 ROWS ONLY)
            </when>
            <when test="sort =='Top 50 Sales'.toString()">
                WITH BASE AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(SALES_VALUE) DESC FETCH FIRST 50 ROWS ONLY)
            </when>
            <when test="sort =='Top 30 Sales'.toString()">
                WITH BASE AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(SALES_VALUE) DESC FETCH FIRST 30 ROWS ONLY)
            </when>
            <otherwise>
                WITH BASE AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                )

            </otherwise>
        </choose>
        select /*+ parallel(t 12) */
        <foreach collection="categroy" separator="," item="item">
            nvl(t.${item},'Others') "${item}"
        </foreach>,
        t.SALES_VALUE,
        t.INVENTORY_VALUE,
        t.INVENTORY_QTY,
        TO_CHAR(t."MONTH", 'YYYY-MM') AS "MONTH",
        t.SPOT_DIN,
        t.NET_VALUE
        from ${SCPA.DISTRIBUTOR_INVENTORY_V} t
        INNER JOIN BASE B ON B.CUSTOMER_NAME=t.CUSTOMER_NAME
        <where>
            <if test="_filters != null and _filters != ''.toString()">
                ${_filters}
            </if>
            AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
            to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
        </where>
        group by
        <foreach collection="categroy" separator="," item="item">
            nvl(t.${item},'Others')
        </foreach>,
        SALES_VALUE,
        INVENTORY_VALUE,
        INVENTORY_QTY,
        "MONTH",
        SPOT_DIN,
        NET_VALUE
        order by
        <foreach collection="categroy" separator="," item="item">
            decode (${item},'Others','zzz',${item})
        </foreach>,
        SALES_VALUE,
        INVENTORY_VALUE,
        INVENTORY_QTY,
        "MONTH",
        SPOT_DIN,
        NET_VALUE


    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSql">
        SELECT
            CUSTOMER_CODE,
            CUSTOMER_NAME,
            PARENT_COMPANY_CODE,
            TO_CHAR(t."MONTH", 'YYYY-MM') AS "MONTH",
            PRODUCT_TYPE,
            PRODUCTION_LINE,
            PRODUCT_SERIES,
            PRODUCT_FAMILY,
            SALES_VALUE,
            INVENTORY_VALUE,
            INVENTORY_QTY,
            NET_VALUE,
            SPOT_DIN
        from ${SCPA.DISTRIBUTOR_INVENTORY_V} t
        <where>
            <if test="selectedValue != null and selectedValue.isEmpty() == false">
                <foreach collection="categroy" separator=" and " item="item" index="index" open=" and ">
                    <choose>
                        <when test="selectedValue[index] == 'Others'.toString()">
                            (${item} = 'Others' or ${item} is null)
                        </when>
                        <when test="selectedValue[index] != null and selectedValue[index] != ''.toString()">
                            ${item} = #{selectedValue[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="_filters != null and _filters != ''.toString()">
                and ${_filters}
            </if>
        </where>
    </sql>


    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>
    <select id="queryReport2" resultType="java.util.Map">
        <choose>
            <when test="sort =='Top 50 Inventory'.toString()">
                WITH CUSTOMER AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(INVENTORY_VALUE) DESC FETCH FIRST 50 ROWS ONLY),
            </when>
            <when test="sort =='Top 20 Inventory'.toString()">
                WITH CUSTOMER AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(INVENTORY_VALUE) DESC FETCH FIRST 20 ROWS ONLY),
            </when>
            <when test="sort =='Top 50 Sales'.toString()">
                WITH CUSTOMER AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(SALES_VALUE) DESC FETCH FIRST 50 ROWS ONLY),
            </when>
            <when test="sort =='Top 20 Sales'.toString()">
                WITH CUSTOMER AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(SALES_VALUE) DESC FETCH FIRST 20 ROWS ONLY),
            </when>
            <otherwise>
                WITH CUSTOMER AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ),

            </otherwise>
        </choose>
        SPOT_SUM AS (SELECT
        "MONTH",
        SUM(SALES_VALUE) AS SALES_VALUE,
        SUM(INVENTORY_VALUE) AS INVENTORY_VALUE
        from ${SCPA.DISTRIBUTOR_INVENTORY_V} t
        INNER JOIN CUSTOMER B ON B.CUSTOMER_NAME=t.CUSTOMER_NAME
        <where>
            <if test="_filters != null and _filters != ''.toString()">
                ${_filters}
            </if>
            AND "MONTH" between ADD_MONTHS(TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm'),-2) and
            to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
        </where>
        GROUP BY "MONTH"),
        SPOT AS (SELECT MONTH, SALES_VALUE, INVENTORY_VALUE,
                    CASE WHEN INVENTORY_VALUE=0 OR SALES_VALUE=0 THEN 0
                ELSE INVENTORY_VALUE/((SUM(SALES_VALUE) OVER (
        ORDER BY MONTH
        RANGE BETWEEN INTERVAL '2' MONTH PRECEDING AND CURRENT ROW
        )) / 3) *30 END AS SPOT_DIN FROM SPOT_SUM),
        BASE AS (
        SELECT * FROM SPOT T
        <where>
             "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
            to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
        </where>
        )
        SELECT "xAxis",RANGE,case when "RANGE" = 'SPOT DIN' then ROUND(AVG(CNT),0) else SUM(CNT) END AS CNT FROM(
        SELECT TO_CHAR(ADD_MONTHS(LAST_DAY("MONTH"), 0), 'yyyy-mm') AS "xAxis", 'Inventory Value' AS RANGE,
        INVENTORY_VALUE AS CNT
        FROM BASE
        UNION ALL
        SELECT TO_CHAR(ADD_MONTHS(LAST_DAY("MONTH"), 0), 'yyyy-mm') AS "xAxis", 'Sales Value' AS RANGE, SALES_VALUE AS
        CNT
        FROM BASE
        UNION ALL
        SELECT TO_CHAR(ADD_MONTHS(LAST_DAY("MONTH"), 0), 'yyyy-mm') AS "xAxis", 'SPOT DIN' AS RANGE, SPOT_DIN AS CNT
        FROM BASE) MM
        GROUP BY MM."xAxis",MM.RANGE
        ORDER BY MM."xAxis"
    </select>

    <sql id="report3SQL">
        <choose>
            <when test="sort =='Top 50 Inventory'.toString()">
                WITH SORT AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(INVENTORY_VALUE) DESC FETCH FIRST 50 ROWS ONLY),
            </when>
            <when test="sort =='Top 30 Inventory'.toString()">
                WITH SORT AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(INVENTORY_VALUE) DESC FETCH FIRST 30 ROWS ONLY),
            </when>
            <when test="sort =='Top 50 Sales'.toString()">
                WITH SORT AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(SALES_VALUE) DESC FETCH FIRST 50 ROWS ONLY),
            </when>
            <when test="sort =='Top 30 Sales'.toString()">
                WITH SORT AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ORDER BY SUM(SALES_VALUE) DESC FETCH FIRST 30 ROWS ONLY),
            </when>
            <otherwise>
                WITH SORT AS (
                SELECT NVL(CUSTOMER_NAME, 'Others') "CUSTOMER_NAME"
                FROM ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm') and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY CUSTOMER_NAME
                ),

            </otherwise>
        </choose>
        <choose>
            <when test="report3ResultType =='SPOT_DIN'.toString()">
        SPOT_SUM AS (SELECT
        <foreach collection="categroy" item="item">
            NVL(T.${item}, 'Others')               AS ${item},
        </foreach>
        "MONTH",
        SUM(SALES_VALUE) AS SALES_VALUE,
        SUM(INVENTORY_VALUE) AS INVENTORY_VALUE
        from ${SCPA.DISTRIBUTOR_INVENTORY_V} t
        INNER JOIN SORT S ON S.CUSTOMER_NAME=t.CUSTOMER_NAME
        <where>
            <if test="_filters != null and _filters != ''.toString()">
                ${_filters}
            </if>
            AND "MONTH" between ADD_MONTHS(TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm'),-2) and
            to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
        </where>
        GROUP BY
        <foreach collection="categroy" item="item">
            T.${item},
        </foreach>
        "MONTH"),
        SPOT_TOTAL AS (
                SELECT
                "MONTH",
                SUM(SALES_VALUE) AS SALES_VALUE,
                SUM(INVENTORY_VALUE) AS INVENTORY_VALUE
                from ${SCPA.DISTRIBUTOR_INVENTORY_V} t
                INNER JOIN SORT S ON S.CUSTOMER_NAME=t.CUSTOMER_NAME
                <where>
                    <if test="_filters != null and _filters != ''.toString()">
                        ${_filters}
                    </if>
                    AND "MONTH" between ADD_MONTHS(TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm'),-2) and
                    to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                </where>
                GROUP BY
                "MONTH"
                ),
            BASE AS (
                SELECT
                <foreach collection="categroy" item="item">
                    NVL(T.${item}, 'Others')               AS ${item},
                </foreach>
                TO_CHAR(T.MONTH, 'YYYY-MM')               AS CALENDAR_DATE,
                        CASE WHEN (SUM(SALES_VALUE) OVER ( PARTITION BY
                <foreach collection="categroy" item="item" separator=",">
                    ${item}
                </foreach>
                ORDER BY MONTH RANGE BETWEEN INTERVAL '2' MONTH PRECEDING AND CURRENT ROW ) )= 0 OR INVENTORY_VALUE = 0  THEN 0 ELSE
                ROUND(nvl(INVENTORY_VALUE/(SUM(SALES_VALUE) OVER ( PARTITION BY
                <foreach collection="categroy" item="item" separator=",">
                    ${item}
                </foreach>
                ORDER BY MONTH RANGE BETWEEN INTERVAL '2' MONTH PRECEDING AND CURRENT ROW ) / 3) *30,0), 3)    END                 AS VALUE
                FROM SPOT_SUM t
            ),
            BASE_TOTAL AS (
                SELECT
                TO_CHAR(T.MONTH, 'YYYY-MM')               AS CALENDAR_DATE,
                CASE WHEN (SUM(SALES_VALUE) OVER (
                ORDER BY MONTH RANGE BETWEEN INTERVAL '2' MONTH PRECEDING AND CURRENT ROW ) )= 0 OR INVENTORY_VALUE = 0  THEN 0 ELSE
                ROUND(nvl(INVENTORY_VALUE/(SUM(SALES_VALUE) OVER (
                ORDER BY MONTH RANGE BETWEEN INTERVAL '2' MONTH PRECEDING AND CURRENT ROW ) / 3) *30,0), 3)    END                 AS VALUE
                FROM SPOT_TOTAL t
                )
            SELECT * FROM
            ( SELECT
            <foreach collection="categroy" item="item">
                NVL(T.${item}, 'Others')         AS ${item},
            </foreach>
            CALENDAR_DATE,
            NVL(T.VALUE, 0) AS TOTAL
            FROM BASE T
            UNION ALL
            SELECT
                <foreach collection="categroy" item="item">
                    'Total'         AS ${item},
                </foreach>
                CALENDAR_DATE,
                NVL(T.VALUE, 0) AS TOTAL
            FROM BASE_TOTAL T
            ) MM
            PIVOT (
            SUM(TOTAL) AS TOTAL
            FOR CALENDAR_DATE
            IN (
            <foreach collection="report3ColumnNames" separator="," item="item">
                '${item}'
            </foreach>)
            )
            ORDER BY
            <foreach collection="categroy" item="item" separator=",">
                CASE WHEN DECODE(${item}, 'Others', 'zzz', ${item})='Total' THEN 1 ELSE 0 END
            </foreach>

        </when>
        <otherwise>
            BASE AS (
            SELECT
            <foreach collection="categroy" item="item">
                NVL(${item}, 'Others')               AS ${item},
            </foreach>
            TO_CHAR(T.MONTH, 'YYYY-MM')               AS CALENDAR_DATE,
            ROUND(nvl(${report3ValueColumn},0), 3)                     AS VALUE
            FROM SCPA.DISTRIBUTOR_INVENTORY_V t
            <where>
                <if test="_filters != null and _filters != ''.toString()">
                    ${_filters}
                </if>
                AND T.MONTH BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm')
                AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
                AND TRUNC(T.MONTH,'MM') = T.MONTH
            </where>
            GROUP BY
            <foreach collection="categroy" item="item">
                ${item},
            </foreach>
            T.MONTH
            )
            SELECT * FROM
            ( SELECT
            <foreach collection="categroy" item="item">
                NVL(${item}, 'Others')         AS ${item},
            </foreach>
            CALENDAR_DATE,
            NVL(T.VALUE, 0) AS TOTAL
            FROM BASE T
            ) MM
            PIVOT (
            SUM(TOTAL) AS TOTAL
            FOR CALENDAR_DATE
            IN (
            <foreach collection="report3ColumnNames" separator="," item="item">
                '${item}'
            </foreach>)
            )
            ORDER BY
            <foreach collection="categroy" item="item" separator=",">
                DECODE(${item}, 'Others', 'zzz', ${item})
            </foreach>
        </otherwise>
        </choose>
    </sql>

    <select id="queryReport3Columns" resultType="java.lang.String">
        SELECT DISTINCT TO_CHAR(t.MONTH, 'YYYY-MM') AS RESULT
        FROM SCPA.DISTRIBUTOR_INVENTORY_V t
        WHERE TRUNC(t.MONTH, 'dd') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyymm')
        AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyymm')
        AND TRUNC(T.MONTH,'MM') = T.MONTH
        ORDER BY t.MONTH
        OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3SQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
