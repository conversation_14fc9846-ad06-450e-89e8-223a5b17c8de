package com.scp.customer.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IEndToEndDeliveryTrackingService {

    Response initPage();

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4Details(Map<String, Object> parameterMap);

    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport5Details(Map<String, Object> parameterMap);

    void downloadReport5Details(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadConfiguration(String userid, Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryConfiguration(String userid, Map<String, Object> parameterMap);

    Response saveConfiguration(String userid, Map<String, Object> parameterMap);

    void downloadCotConfiguration(String userid, Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryCotConfiguration(String userid, Map<String, Object> parameterMap);

    Response saveCotConfiguration(String userid, Map<String, Object> parameterMap);

    Response initRCAPage();

    Response queryRCAColumnsByDaterange(Map<String, Object> parameterMap);

    Response queryRCAWeekly(Map<String, Object> parameterMap);

    void downloadRCAWeeklyDetails(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryRCAWeeklyDetails(Map<String, Object> parameterMap);

    Response saveRCAWeeklyDetails(Map<String, Object> parameterMap);

    Response queryRCATipsList(Map<String, Object> parameterMap);

    Response queryManualD0(String userid, Map<String, Object> parameterMap);

    Response saveManualD0(String userid, Map<String, Object> parameterMap);

    void downloadManualD0(String userid, Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryManualD0Cascader();

    Response queryManualD0Details(String userid, Map<String, Object> parameterMap);

    void downloadManualD0Details(String userid, Map<String, Object> parameterMap, HttpServletResponse res);

    Response saveFeedback(String userid, Map<String, Object> parameterMap);
}
