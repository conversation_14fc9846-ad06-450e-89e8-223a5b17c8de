package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.customer.bean.TEXReport1Bean;
import com.scp.customer.bean.TEXReport1Treemap;
import com.scp.customer.bean.TEXReport3Bean;
import com.scp.customer.bean.TEXReport6Bean;
import com.scp.customer.dao.ITEXDao;
import com.scp.customer.service.ITEXService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("TEXService")
@Scope("prototype")
@Transactional
public class TEXServiceImpl extends ServiceHelper implements ITEXService {

    @Resource
    private ITEXDao texdao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryCascader() {
        Map<String, Object> result = new HashMap<>();
        result.put("CASCADER", Utils.parseCascader(texdao.queryCascader()));
        result.put("dateColumns", texdao.queryDateColumns());

        return response.setBody(result);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report1ResultType")));

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<TEXReport1Treemap> resultList = new ArrayList<>();
        List<TEXReport1Bean> dataList = texdao.queryReport1(parameterMap);
        for (TEXReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    private void convertReport1Data(List<TEXReport1Treemap> list, TEXReport1Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<TEXReport1Treemap> child = new ArrayList<>();
        TEXReport1Treemap root = new TEXReport1Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            TEXReport1Treemap treemap = new TEXReport1Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        TEXReport1Treemap lastNode = new TEXReport1Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<TEXReport1Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            TEXReport1Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> report4SelectedColumns = (List<String>) parameterMap.get("report4SelectedColumns");

        if (report4SelectedColumns == null || report4SelectedColumns.isEmpty()) {
            report4SelectedColumns = new ArrayList<>();
            report4SelectedColumns.add("TEX_TYPE");
        }
        parameterMap.put("report4SelectedColumns", report4SelectedColumns);
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("report1ResultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).toList();
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(List.of(""));

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                if (polymorphicColumns.contains(c)) {
                    if ("Overall Cycle Time".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                    } else if ("Overall Cycle Time Closed".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                    } else if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(CASE WHEN " + c + " > 0 THEN 1 ELSE 0 END),0) AS " + tooltip);
                    }
                } else {
                    tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnName(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private String generateValueColumn(String resultType) {
        String valueColumn = "NVL(AVG(OVERALL_CYCLE_TIME), 0)";
        if ("Overall Cycle Time".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(AVG(OVERALL_CYCLE_TIME), 0)";
        } else if ("Overall Cycle Time Closed".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(AVG(OVERALL_CYCLE_TIME_CLOSED), 0)";
        } else if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        } else if ("Avg OCT".equalsIgnoreCase(resultType)) {
            valueColumn = "AVG(DECODE(STATUS, 'Closed', OVERALL_CYCLE_TIME, NULL))";
        }
        return valueColumn;
    }

    private void generateReport2Dimension(Map<String, Object> parameterMap) {
        JSONArray categoryArray = (JSONArray) parameterMap.get("report2DimensionsList");
        List<String> colList = new ArrayList<>();
        if (categoryArray != null) {
            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);
                String calcMethod = subArray.getString(1);
                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }
                colList.add("TO_NUMBER(" + calcMethod + "(" + columnName + ")) AS \"" + calcMethod + "(" + columnName + ")\"");
            }
            parameterMap.put("report2Dimensions", String.join(", \n", colList));
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport2Dimension(parameterMap);
        this.generateTreePathFilter(parameterMap);

        JSONArray report2OrderByList = (JSONArray) parameterMap.get("report2OrderBy");
        String report2OrderBy = report2OrderByList.getString(1) + "(NVL(" + report2OrderByList.getString(0) + ",0))";
        parameterMap.put("report2OrderBy", report2OrderBy);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<LinkedHashMap<String, Object>> dataList = texdao.queryReport2(parameterMap);
        List<List<Double>> boxplotData = dataList.stream().map(row -> {
            java.util.function.Function<Object, Double> toDouble = value -> {
                if (value == null) return null;
                if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                }
                if (value instanceof String && !((String) value).trim().isEmpty()) {
                    try {
                        return Double.parseDouble((String) value);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
                return null;
            };

            return Arrays.asList(
                    toDouble.apply(row.get("MIN_OVERALL_CYCLE_TIME")),
                    toDouble.apply(row.get("Q1_OVERALL_CYCLE_TIME")),
                    toDouble.apply(row.get("Q2_OVERALL_CYCLE_TIME")),
                    toDouble.apply(row.get("Q3_OVERALL_CYCLE_TIME")),
                    toDouble.apply(row.get("MAX_OVERALL_CYCLE_TIME"))
            );
        }).collect(Collectors.toList());

        Set<String> legend = new HashSet<>();
        List<String> xAxisList = new ArrayList<>();

        Set<String> excludedKeys = Set.of("xAxis", "MIN_OVERALL_CYCLE_TIME", "Q1_OVERALL_CYCLE_TIME", "Q2_OVERALL_CYCLE_TIME", "Q3_OVERALL_CYCLE_TIME", "MAX_OVERALL_CYCLE_TIME");

        dataList.forEach(data -> {
            xAxisList.add((String) data.get("xAxis"));
            data.keySet().stream()
                    .filter(key -> !excludedKeys.contains(key))  // 正确：检查 key 是否在排除列表中
                    .forEach(legend::add);
        });

        legend.forEach(key -> {
            List<BigDecimal> temp = dataList.stream()
                    .map(data -> (BigDecimal) data.getOrDefault(key, BigDecimal.ZERO))
                    .collect(Collectors.toList());
            resultMap.put(key, temp);
        });

        resultMap.put("legend", legend);
        resultMap.put("xAxis", xAxisList);
        resultMap.put("boxplot", boxplotData);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(texdao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(texdao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "tex_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ITEXDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report3ResultType")));

        List<TEXReport3Bean> dataList;

        dataList = texdao.queryReport3(parameterMap);
        BigDecimal totalLine = texdao.queryReport3TotalLine(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        // 先将数据转存到dataMap、xAxisMap
        for (TEXReport3Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
            if (!legend.contains(data.getNAME())) {
                legend.add(data.getNAME());
            }
        }
        // 获取x轴
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        //对于每一个图例做处理，生成{图例:[VALUE]}的map
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();
            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        List<BigDecimal> totalLineList = new ArrayList<>();
        for (String x : xAxisList) {
            totalLineList.add(totalLine);
        }

        // 放置x轴
        resultMap.put("xAxis", xAxisList);
        resultMap.put("totalLineList", totalLineList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report3ResultType")));

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(texdao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(texdao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report3ResultType")));

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "tex_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ITEXDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report4ResultType")));

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = texdao.queryReport4(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report4ResultType")));

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(texdao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(texdao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Columns(Map<String, Object> parameterMap) {
        return response.setBody(texdao.queryReport4Columns(parameterMap));
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report4ResultType")));

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "tex_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.ITEXDao.queryReport4", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        parameterMap.put("valueColumn", this.generateValueColumn((String) parameterMap.get("report4ResultType")));

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "tex_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ITEXDao.queryReport4Details", parameterMap);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport5(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        return response.setBody(texdao.queryReport5(parameterMap));
    }

    @Override
    public Response queryReport5Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(texdao.queryReport5DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(texdao.queryReport5Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport5Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "tex_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.ITEXDao.queryReport5Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport6(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<TEXReport6Bean> dataList;

        dataList = texdao.queryReport6(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        // 先将数据转存到dataMap、xAxisMap
        for (TEXReport6Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getXAXIS(), "");
            if (!legend.contains(data.getNAME())) {
                legend.add(data.getNAME());
            }
        }
        // 获取x轴
        List<String> xAxisList = xAxisMap.keySet().stream().sorted((s1, s2) -> xAxisOrder.getOrder(s1) - xAxisOrder.getOrder(s2)).collect(Collectors.toList());
        //对于每一个图例做处理，生成{图例:[VALUE]}的map
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();
            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        // 放置x轴
        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    // 指定顺序
    public enum xAxisOrder {
        CASE_TO_TEX(5, "Case To TEX"),
        OPEN_TO_START(4, "Open To Start"),
        START_TO_COMPLETE(3, "Start To Complete"),
        COMPLETE_TO_REVIEW(2, "Complete To Review"),
        REVIEW_TO_DELIVERY(1, "Review To Delivery"),
        DELIVERY_TO_CLOSE(0, "Delivery To Close");

        private final int order;
        private final String value;

        xAxisOrder(int order, String value) {
            this.order = order;
            this.value = value;
        }

        public int getOrder() {
            return order;
        }

        public String getValue() {
            return value;
        }

        public static int getOrder(String value) {
            for (xAxisOrder order : values()) {
                if (order.getValue().equals(value)) {
                    return order.getOrder();
                }
            }
            return Integer.MAX_VALUE; // 如果没有找到，返回一个默认值
        }
    }
}


