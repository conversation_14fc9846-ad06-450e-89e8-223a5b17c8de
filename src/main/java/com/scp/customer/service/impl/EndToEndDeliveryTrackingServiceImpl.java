package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.customer.bean.EndToEndDeliveryTrackingReport1Bean;
import com.scp.customer.bean.EndToEndDeliveryTrackingReport1Treemap;
import com.scp.customer.bean.EndToEndDeliveryTrackingReport3Bean;
import com.scp.customer.dao.IEndToEndDeliveryTrackingDao;
import com.scp.customer.service.IEndToEndDeliveryTrackingService;
import com.starter.context.bean.*;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.servlet.ServiceHelper;
import com.scp.customer.EndToEndDeliveryTrackingController;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class EndToEndDeliveryTrackingServiceImpl extends ServiceHelper implements IEndToEndDeliveryTrackingService {

    @Resource
    private Response response;

    @Resource
    private IEndToEndDeliveryTrackingDao endToEndDeliveryTrackingDao;

    @Resource
    private ExcelTemplate excelTemplate;


    @Resource
    private ScpTableHelper scpTableHelper;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(endToEndDeliveryTrackingDao.queryCascader()));
        return response.setBody(resultMap);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<EndToEndDeliveryTrackingReport1Treemap> resultList = new ArrayList<>();
        List<EndToEndDeliveryTrackingReport1Bean> dataList = endToEndDeliveryTrackingDao.queryReport1(parameterMap);
        for (EndToEndDeliveryTrackingReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    private void convertReport1Data(List<EndToEndDeliveryTrackingReport1Treemap> list, EndToEndDeliveryTrackingReport1Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<EndToEndDeliveryTrackingReport1Treemap> child = new ArrayList<>();
        EndToEndDeliveryTrackingReport1Treemap root = new EndToEndDeliveryTrackingReport1Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            EndToEndDeliveryTrackingReport1Treemap treemap = new EndToEndDeliveryTrackingReport1Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        EndToEndDeliveryTrackingReport1Treemap lastNode = new EndToEndDeliveryTrackingReport1Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<EndToEndDeliveryTrackingReport1Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            EndToEndDeliveryTrackingReport1Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).toList();
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(List.of(""));

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                if (polymorphicColumns.contains(c)) {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                    } else if ("Value".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "_VALUE),0) AS " + tooltip);
                    } else if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(CASE WHEN " + c + " > 0 THEN 1 ELSE 0 END),0) AS " + tooltip);
                    }
                } else {
                    tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnName(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "COUNT(1)";
        if ("Count".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        return response.setBody(endToEndDeliveryTrackingDao.queryReport2(parameterMap));
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<EndToEndDeliveryTrackingReport3Bean> dataList;

        String report3DateType = (String) parameterMap.get("report3DateType");
        if (StringUtils.equals(report3DateType, "VIEW_BY_DAY")) {
            parameterMap.put("dateColumn", "CALENDAR_DATE");
        } else if (StringUtils.equals(report3DateType, "VIEW_BY_WEEK")) {
            parameterMap.put("dateColumn", "CALENDAR_WEEK");
        } else if (StringUtils.equals(report3DateType, "VIEW_BY_MONTH")) {
            parameterMap.put("dateColumn", "CALENDAR_MONTH");
        } else if (StringUtils.equals(report3DateType, "VIEW_BY_QUARTER")) {
            parameterMap.put("dateColumn", "CALENDAR_QUARTER");
        } else if (StringUtils.equals(report3DateType, "VIEW_BY_YEAR")) {
            parameterMap.put("dateColumn", "CALENDAR_YEAR");
        } else {
            parameterMap.put("dateColumn", "CALENDAR_WEEK");
        }

        dataList = endToEndDeliveryTrackingDao.queryReport3(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        // 先将数据转存到dataMap、xAxisMap
        for (EndToEndDeliveryTrackingReport3Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR(), "");
            if (legend.contains(data.getNAME()) == false) {
                legend.add(data.getNAME());
            }
        }
        // 获取x轴
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        //对于每一个图例做处理，生成{图例:[VALUE]}的map
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();
            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        // 放置x轴
        parameterMap.put("xAxis", xAxisList);
        resultMap.put("xAxis", xAxisList);

        Map<String, Object> lineData = endToEndDeliveryTrackingDao.queryReport3Line(parameterMap);
        Map<String, Object> replacedMap = new HashMap<>();
        if (lineData != null) {
            for (Map.Entry<String, Object> entry : lineData.entrySet()) {
                String newKey = entry.getKey().replace("'", "");
                replacedMap.put(newKey, entry.getValue());
            }
        }
        List<String> lineResult = new ArrayList<>();
        for (String x : xAxisList) {
            lineResult.add(replacedMap.get(x).toString());
        }
        resultMap.put("lineData", lineResult);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndDeliveryTrackingDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "end_to_end_delivery_tracking_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryReport3Details", parameterMap);
    }

    @Override
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(endToEndDeliveryTrackingDao.queryReport4Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryReport4(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "end_to_end_delivery_tracking_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryReport4", parameterMap);
    }

    @Override
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(endToEndDeliveryTrackingDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryReport4Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "end_to_end_delivery_tracking_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryReport4Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport5(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        return response.setBody(endToEndDeliveryTrackingDao.queryReport5(parameterMap));
    }

    @Override
    public Response queryReport5Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndDeliveryTrackingDao.queryReport5DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryReport5Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport5Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "end_to_end_delivery_tracking_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryReport5Details", parameterMap);
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            parameterMap.put("categroy", categroyDefault);
        }
    }

    @Override
    public Response queryConfiguration(String userid, Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndDeliveryTrackingDao.queryConfigurationCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryConfiguration(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadConfiguration(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_delivery_configuration" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryConfiguration", parameterMap);
    }

    @Override
    public Response saveConfiguration(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
        }});

        String role = endToEndDeliveryTrackingDao.queryPageAdmin(userid, EndToEndDeliveryTrackingController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "DC_ADMIN".equalsIgnoreCase(role);

        scpTableHelper.setScpTableInsertHandler((headers, creates) -> endToEndDeliveryTrackingDao.createConfigurationByTable(headers, creates, userid));
        scpTableHelper.setScpTableDeleteHandler(deletes -> endToEndDeliveryTrackingDao.deleteConfigurationByTable(deletes, userid, isAdmin));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> endToEndDeliveryTrackingDao.updateConfigurationByTable(pk, updates, userid, isAdmin));
        scpTableHelper.setWarningMessage("Only ADMIN can modify the data");
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryCotConfiguration(String userid, Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndDeliveryTrackingDao.queryCotConfigurationCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryCotConfiguration(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCotConfiguration(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_delivery_cot_configuration" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryCotConfiguration", parameterMap);
    }

    @Override
    public Response saveCotConfiguration(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
        }});

        String role = endToEndDeliveryTrackingDao.queryPageAdmin(userid, EndToEndDeliveryTrackingController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "DC_ADMIN".equalsIgnoreCase(role);

        scpTableHelper.setScpTableInsertHandler((headers, creates) -> endToEndDeliveryTrackingDao.createCotConfigurationByTable(headers, creates, userid));
        scpTableHelper.setScpTableDeleteHandler(deletes -> endToEndDeliveryTrackingDao.deleteCotConfigurationByTable(deletes, userid, isAdmin));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> endToEndDeliveryTrackingDao.updateCotConfigurationByTable(pk, updates, userid, isAdmin));
        scpTableHelper.setWarningMessage("Only ADMIN can modify the data");
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    // End to End Delivery RCA Region Start
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initRCAPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rcaCode", endToEndDeliveryTrackingDao.queryRCACode());
        resultMap.put("weekOpts", endToEndDeliveryTrackingDao.queryWeekOpts());

        List<LinkedHashMap<String, Object>> tipsList = endToEndDeliveryTrackingDao.queryRCATips();
        Map<String, String> tipsMap = new HashMap<>();
        for (Map<String, Object> map : tipsList) {
            String des = (String) map.get("DESCRIPTION");
            String logic = (String) map.get("COMPUTING_LOGIC");
            if (logic != null) {
                des += ". " + logic;
            }
            tipsMap.put(String.valueOf(map.get("RCA_TIPS_CODE")), des);
        }
        resultMap.put("rcaTips", tipsMap);
        resultMap.put("cascader", Utils.parseCascader(endToEndDeliveryTrackingDao.queryCascader()));
        return response.setBody(resultMap);
    }

    /**
     * calc weeks and days between start month and end month
     *
     * @param parameterMap parameters
     * @return result map
     */
    private Map<String, List<String>> queryColumnsByDaterangeSource(Map<String, Object> parameterMap) {
        Map<String, List<String>> resultMap = new HashMap<>();

        resultMap.put("weeks", endToEndDeliveryTrackingDao.queryRCAWeeklyWeekColumns(parameterMap));
        resultMap.put("months", endToEndDeliveryTrackingDao.queryRCAWeeklyMonthColumns(parameterMap));
        resultMap.put("years", endToEndDeliveryTrackingDao.queryRCAWeeklyYearColumns(parameterMap));

        return resultMap;
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryRCAWeekly(Map<String, Object> parameterMap) {
        this.generateRCACascaderFilter(parameterMap, "DIVISION");
        String reportOrderBy = (String) parameterMap.get("reportOrderBy");
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        List<String> fields = (List<String>) parameterMap.get("field");
        String sort = page.getSort();
        if (!fields.contains(page.getSortColumn())) {
            sort = sort.replace("\"", "\"'");
            if (Objects.equals(reportOrderBy, "ONTIME") || Objects.equals(reportOrderBy, "FAIL")) {
                sort = sort.replace("\"' ", "'_" + reportOrderBy + "\" ");
            } else {
                sort = sort.replace("\"' ", "'_RATIO\" ");
            }

            page.setSort(sort);
        }

        Map<String, List<String>> columnMap = this.queryColumnsByDaterangeSource(parameterMap);
        parameterMap.putAll(columnMap);

        parameterMap.put("defaultOrderColumn", columnMap.get("years").get(0));
        List<Map<String, Object>> dataList = endToEndDeliveryTrackingDao.queryRCAWeekly(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();

        List<String> columns = columnMap.get("years");
        columns.addAll(columnMap.get("months"));
        columns.addAll(columnMap.get("weeks"));

        Map<String, Object> tempMap;
        for (Map<String, Object> map : dataList) {
            // RATIO
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "RATIO");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_RATIO"));
            }
            resultList.add(tempMap);
            // On Time
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "On Time");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_ONTIME"));
            }
            resultList.add(tempMap);

            // Delay
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "Delay");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_FAIL"));
                tempMap.put(c + "_CONFIRMED", map.get("'" + c + "'_CONFIRMED"));
            }
            resultList.add(tempMap);
        }

        this.generateRCASummary(fields, resultList);
        page.setData(resultList);
        page.setTotal(resultList.size());

        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryRCAColumnsByDaterange(Map<String, Object> parameterMap) {
        return response.setBody(this.queryColumnsByDaterangeSource(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryRCAWeeklyDetails(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateRCACascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }
        page.setTotal(endToEndDeliveryTrackingDao.queryRCAWeeklyDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryRCAWeeklyDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveRCAWeeklyDetails(Map<String, Object> parameterMap) {
        Object obj = parameterMap.get("weeklyDetailsUpdate");
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (String key : jsonObj.keySet()) {
                JSONObject changeObj = jsonObj.getJSONObject(key);
                Map<String, Object> map = new HashMap<>();
                String[] keys = key.split("#");
                map.put("order", keys[0]);
                map.put("item", keys[1]);
                map.put("rca_result", changeObj.getString("RCA_RESULT"));
                map.put("rca_comments", changeObj.getString("RCA_COMMENTS"));
                map.put("type", 'W');
                dataList.add(map);
            }
            if (dataList.isEmpty() == false) {
                parameterMap.put("dataList", dataList);
                endToEndDeliveryTrackingDao.saveRCAResult(parameterMap);
            }
        }
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryRCATipsList(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndDeliveryTrackingDao.queryRCATipsListCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryRCATipsList(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadRCAWeeklyDetails(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateRCACascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }
        String fileName = "e2e_delivery_rca_" + Utils.randomStr(4) + ".xlsx";

        SheetInfoWithQueryKey sheet1 = new SheetInfoWithQueryKey();
        sheet1.setParameterMap(parameterMap);
        sheet1.setQueryKey("com.scp.customer.dao.IEndToEndDeliveryTrackingDao.downloadRCAWeeklyDetails");
        sheet1.setSheetName("Weekly Details");

        SheetInfoWithQueryKey sheet2 = new SheetInfoWithQueryKey();
        sheet2.setQueryKey("com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryRCATips");
        sheet2.setSheetName("RCA Tips Description");

        excelTemplate.create(res, fileName, sheet1, sheet2);
    }

    private void generateRCASummary(List<String> fields, List<Map<String, Object>> resultList) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        for (Map<String, Object> map : resultList) {
            for (String key : map.keySet()) {
                if (fields.contains(key) == false && "TYPE".equals(key) == false && "RATIO".equals(map.get("TYPE")) == false && key.endsWith("_ORDER_BY") == false) {
                    String type = (String) map.get("TYPE");
                    Map<String, Object> subMap = resultMap.computeIfAbsent(type, k -> new HashMap<>());

                    if (subMap.isEmpty()) {
                        for (int i = 0; i < fields.size(); i++) {
                            if (i == fields.size() - 1) {
                                subMap.put(fields.get(i), "Total");
                            }
                        }
                        subMap.put("TYPE", type);
                    }

                    Object totalObject = subMap.get(key); // total cell value
                    Object currentCellObject = map.get(key); // current cell value
                    if (totalObject == null) {
                        subMap.put(key, currentCellObject);
                    } else {
                        if (currentCellObject != null) {
                            subMap.put(key, Utils.parseBigDecimal(totalObject).add(Utils.parseBigDecimal(currentCellObject)));
                        }
                    }
                }
            }
        }

        if (resultMap.isEmpty() == false) {
            Map<String, Object> otMap = resultMap.get("On Time");
            Map<String, Object> nllMap = resultMap.get("Delay");
            Map<String, Object> ratioMap = new HashMap<>();

            for (int i = 0; i < fields.size(); i++) {
                if (i == fields.size() - 1) {
                    ratioMap.put(fields.get(i), "Total");
                }
            }
            ratioMap.put("TYPE", "RATIO");

            for (String key : nllMap.keySet()) {
                BigDecimal otValue = Utils.parseBigDecimal(otMap.get(key), BigDecimal.ZERO);
                BigDecimal nllValue = Utils.parseBigDecimal(nllMap.get(key), BigDecimal.ZERO);
                BigDecimal total = otValue.add(nllValue);
                if (total.compareTo(BigDecimal.ZERO) > 0) {
                    ratioMap.put(key, otValue.divide(total, 6, RoundingMode.HALF_UP));
                }
            }

            resultList.add(ratioMap);
            resultList.add(otMap);
            resultList.add(nllMap);
        }
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    @SuppressWarnings("unchecked")
    private void generateRCACascaderFilter(Map<String, Object> parameterMap, String... blackList) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, null, "T", "_filters", blackList);

        // 处理field,防止传入空值
        List<String> defaultField = new ArrayList<>();
        defaultField.add("ENTITY");
        List<String> fields = (List<String>) parameterMap.get("field");
        if (fields.isEmpty()) {
            parameterMap.put("field", defaultField);
        } else {
            for (String field : fields) {
                if (Utils.hasInjectionAttack(field)) {
                    parameterMap.put("field", defaultField);
                    break;
                }
            }
        }
    }

    // End to End Delivery RCA Region Start

    @Override
    public Response queryManualD0Cascader() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(endToEndDeliveryTrackingDao.queryManualD0Cascader()));
        resultMap.put("report2Columns", endToEndDeliveryTrackingDao.queryReport2AvailableColumns());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryManualD0(String userid, Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndDeliveryTrackingDao.queryManualD0Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryManualD0(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadManualD0(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_delivery_manual_d0" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryManualD0", parameterMap);
    }

    @Override
    public Response saveManualD0(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
        }});

        String role = endToEndDeliveryTrackingDao.queryPageAdmin(userid, EndToEndDeliveryTrackingController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "SOA_ADMIN".equalsIgnoreCase(role);

        scpTableHelper.setScpTableInsertHandler((headers, creates) -> endToEndDeliveryTrackingDao.createManualD0(headers, creates, userid));
        scpTableHelper.setScpTableDeleteHandler(deletes -> endToEndDeliveryTrackingDao.deleteManualD0(deletes, userid, isAdmin));
        scpTableHelper.setWarningMessage("Only ADMIN can modify the data");
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryManualD0Details(String userid, Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndDeliveryTrackingDao.queryManualD0DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndDeliveryTrackingDao.queryManualD0Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadManualD0Details(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_delivery_manual_d0_details" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IEndToEndDeliveryTrackingDao.queryManualD0Details", parameterMap);
    }

    @Override
    public Response saveFeedback(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setWarningMessage("Only ADMIN can modify the data");
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> {
                    String[] pks = pk.split(",");
                    String deliveryNumber = pks[0];
                    int count = endToEndDeliveryTrackingDao.queryFeedbackExists(deliveryNumber, userid);
                    if (count == 0) {
                        return 0;
                    } else {
                        return endToEndDeliveryTrackingDao.updateFeedback(deliveryNumber, updates, userid);
                    }
                }
        );

        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }
}
