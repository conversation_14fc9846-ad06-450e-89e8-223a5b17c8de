package com.scp.customer.service.impl;

import com.scp.customer.dao.IBOLDao;
import com.scp.customer.service.IBOLService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class BOLServiceImpl extends ServiceHelper implements IBOLService {

    @Resource
    private Response response;

    @Resource
    private IBOLDao bolDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("report1Headers", bolDao.queryReport1Columns(parameterMap));
        resultMap.put("cascader", Utils.parseCascader(bolDao.queryCascader()));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();
        List<String> days = bolDao.queryReport1Columns(parameterMap);
        if (days.isEmpty()) {
            resultMap.put("headers", new ArrayList<>());
            resultMap.put("data", new ArrayList<>());
            return response.setBody(resultMap);
        }
        parameterMap.put("days", days);

        resultMap.put("headers", bolDao.queryReport1Columns(parameterMap));
        resultMap.put("data", bolDao.queryReport1(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Sub(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        parameterMap.put("days", bolDao.queryReport1Columns(parameterMap));
        return response.setBody(bolDao.queryReport1Sub(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bolDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bolDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bol_details_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IBOLDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();

        List<String> xAxis = new ArrayList<>();
        List<BigDecimal> yAxis = new ArrayList<>();
        List<BigDecimal> yAxis1 = new ArrayList<>();
        List<BigDecimal> yAxis2 = new ArrayList<>();
        List<BigDecimal> yAxis3 = new ArrayList<>();
        List<BigDecimal> yAxis4 = new ArrayList<>();
        List<BigDecimal> yAxis5 = new ArrayList<>();
        List<BigDecimal> yAxis6 = new ArrayList<>();
        List<BigDecimal> yAxis7 = new ArrayList<>();
        List<BigDecimal> yAxis8 = new ArrayList<>();
        List<Map<String, Object>> dataList = bolDao.queryReport2(parameterMap);

        for (Map<String, Object> map : dataList) {
            xAxis.add((String) map.get("xAxis"));
            yAxis.add(Utils.parseBigDecimal(map.get("yAxis")));
            yAxis1.add(Utils.parseBigDecimal(map.get("yAxis1")));
            yAxis2.add(Utils.parseBigDecimal(map.get("yAxis2")));
            yAxis3.add(Utils.parseBigDecimal(map.get("yAxis3")));
            yAxis4.add(Utils.parseBigDecimal(map.get("yAxis4")));
            yAxis5.add(Utils.parseBigDecimal(map.get("yAxis5")));
            yAxis6.add(Utils.parseBigDecimal(map.get("yAxis6")));
            yAxis7.add(Utils.parseBigDecimal(map.get("yAxis7")));
            yAxis8.add(Utils.parseBigDecimal(map.get("yAxis8")));
        }

        resultMap.put("xAxis", xAxis);
        resultMap.put("yAxis", yAxis);
        resultMap.put("yAxis1", yAxis1);
        resultMap.put("yAxis2", yAxis2);
        resultMap.put("yAxis3", yAxis3);
        resultMap.put("yAxis4", yAxis4);
        resultMap.put("yAxis5", yAxis5);
        resultMap.put("yAxis6", yAxis6);
        resultMap.put("yAxis7", yAxis7);
        resultMap.put("yAxis8", yAxis8);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        List<Map<String, Object>> dataList = bolDao.queryReport3(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("name", map.get("KEY"));
            resultMap.put("value", map.get("VAL"));
            resultList.add(resultMap);
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bolDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bolDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bol_details_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IBOLDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport6(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();

        List<String> legends = new ArrayList<>();
        List<String> xAxis = new ArrayList<>();
        List<Map<String, Object>> legendList = bolDao.queryReport6Legend(parameterMap);
        for (Map<String, Object> map : legendList) {
            legends.add((String) map.get("BOL_RCA"));
        }
        parameterMap.put("legends", legends);
        List<Map<String, Object>> dataList = bolDao.queryReport6(parameterMap);

        for (String i : legends) {
            List<BigDecimal> temp = new ArrayList<>();
            for (Map<String, Object> map : dataList) {
                temp.add(Utils.parseBigDecimal(map.get(i)));
            }
            resultMap.put(i, temp);
        }
        for (Map<String, Object> map : dataList) {
            xAxis.add((String) map.get("xAxis"));
        }
        resultMap.put("xAxis", xAxis);
        return response.setBody(resultMap);
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        String valueType = (String) parameterMap.get("valueType");
        String orderType = (String) parameterMap.get("orderType");
        String qtyColumn = "OPEN_SO_W_O_GI";
        if ("OPEN_SO_W_O_GI".equals(orderType)) {
            qtyColumn = "OPEN_SO_W_O_GI";
        } else if ("OPEN_SO_W_O_DEL".equals(orderType)) {
            qtyColumn = "OPEN_SO_W_O_DEL";
        }

        String valueColumn = "NET_NET_VALUE_RMB";
        String targeColumn = "0";
        if ("Net Net Price".equalsIgnoreCase(valueType)) {
            valueColumn = "(AVG_SELLING_PRICE_RMB * " + qtyColumn + ")";
            targeColumn = "SUM(BACK_ORDER_VALUE_TARGET)";
        } else if ("Quantity".equalsIgnoreCase(valueType)) {
            valueColumn = "(" + qtyColumn + ")";
            targeColumn = "SUM(BACK_ORDER_QUANTITY_TARGET)";
        } else if ("Line".equalsIgnoreCase(valueType)) {
            valueColumn = "1";
            targeColumn = "0";
        } else if ("Net Price".equalsIgnoreCase(valueType)) {
            valueColumn = "(NET_PRICE * " + qtyColumn + ")";
            targeColumn = "SUM(BACK_ORDER_VALUE_TARGET)";
        } else if ("Net Net Price HKD".equalsIgnoreCase(valueType)) {
            valueColumn = "(AVG_SELLING_PRICE_HKD * " + qtyColumn + ")";
            targeColumn = "SUM(BACK_ORDER_VALUE_TARGET)";
        } else if ("Weight".equalsIgnoreCase(valueType)) {
            valueColumn = "(" + "GROSS_WEIGHT_IN_KG" + ")";
            targeColumn = "0";
        }
        parameterMap.put("valueColumn", valueColumn);
        parameterMap.put("targeColumn", targeColumn);

        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap);
    }

}
