<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.ITEXDao">

    <sql id="TEXFilter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            AND ${treePathFilter}
        </if>
        <if test="${includeReportDate} == Y">
            AND ${dateColumn} BETWEEN TO_DATE(#{reportDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TO_DATE(#{reportDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </if>
    </sql>

    <resultMap id="report1ResultMap" type="com.scp.customer.bean.TEXReport1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.customer.bean.TEXReport1Tooltips">
            <result property="OVERALL_CYCLE_TIME" column="OVERALL_CYCLE_TIME"/>
            <result property="OVERALL_CYCLE_TIME_CLOSED" column="OVERALL_CYCLE_TIME_CLOSED"/>
        </association>
    </resultMap>

    <resultMap id="columnMap" type="java.lang.String">
        <result column="COLUMN_NAME"/>
    </resultMap>

    <select id="queryDateColumns" resultMap="columnMap" resultType="java.util.List">
        SELECT COLUMN_NAME
        FROM USER_TAB_COLS
        WHERE TABLE_NAME IN ('TECH_EXPERT_ASSESSMENTS_V')
          AND DATA_TYPE = 'DATE'
          AND COLUMN_NAME NOT IN ('CREATE_DATE$', 'UPDATE_DATE$')
        ORDER BY COLUMN_NAME
    </select>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM SCPA.TECH_EXPERT_ASSESSMENTS_FILTER_V T
        ORDER BY CATEGORY
    </select>

    <select id="queryReport1" resultMap="report1ResultMap">
        SELECT NVL(${level1}, 'Others') AS CATEGORY1,
               NVL(${level2}, 'Others') AS CATEGORY2,
               NVL(${level3}, 'Others') AS CATEGORY3,
               <if test="level4 != null and level4 != ''.toString()">
                   NVL(${level4}, 'Others') AS CATEGORY4,
               </if>
               <if test="level5 != null and level5 != ''.toString()">
                   NVL(${level5}, 'Others') AS CATEGORY5,
               </if>
               ${valueColumn} AS VALUE
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                   ,${tooltipsColumns}
               </if>
        FROM ${SCPA.TECH_EXPERT_ASSESSMENTS_V} T
        <where>
            <include refid="TEXFilter">
                <property name="includeReportDate" value="Y"/>
            </include>
        </where>
        GROUP BY ${level1},
                 ${level2},
                 ${level3}
                 <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
                 <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>


    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        SELECT * FROM (SELECT ${report2yAxis}                                                  AS "xAxis",
                              NVL(MAX(TO_NUMBER(DECODE(STATUS, 'Closed', OVERALL_CYCLE_TIME, NULL))), 0)                                          AS MAX_OVERALL_CYCLE_TIME,
                              NVL(MIN(TO_NUMBER(DECODE(STATUS, 'Closed', OVERALL_CYCLE_TIME, NULL))), 0)                                          AS MIN_OVERALL_CYCLE_TIME,
                              NVL(PERCENTILE_DISC(0.25) WITHIN GROUP (ORDER BY TO_NUMBER(DECODE(STATUS, 'Closed', OVERALL_CYCLE_TIME, NULL))), 0) AS Q1_OVERALL_CYCLE_TIME,
                              NVL(PERCENTILE_DISC(0.50) WITHIN GROUP (ORDER BY TO_NUMBER(DECODE(STATUS, 'Closed', OVERALL_CYCLE_TIME, NULL))), 0) AS Q2_OVERALL_CYCLE_TIME,
                              NVL(PERCENTILE_DISC(0.75) WITHIN GROUP (ORDER BY TO_NUMBER(DECODE(STATUS, 'Closed', OVERALL_CYCLE_TIME, NULL))), 0) AS Q3_OVERALL_CYCLE_TIME
                              <if test="report2Dimensions != null and  report2Dimensions != ''.toString()">
                                  ,${report2Dimensions}
                              </if>
                        FROM ${SCPA.TECH_EXPERT_ASSESSMENTS_V} T
                        <where>
                            <include refid="TEXFilter">
                                <property name="includeReportDate" value="Y"/>
                            </include>
                        </where>
                        GROUP BY ${report2yAxis}
                        ORDER BY ${report2OrderBy} DESC)
        FETCH NEXT ${report2TopQuantities} ROWS ONLY
    </select>

    <sql id="report2DetailsSQL">
        SELECT * FROM ${SCPA.TECH_EXPERT_ASSESSMENTS_V} T
        <where>
            <if test="report2SelectedValue != null and report2SelectedValue != ''.toString()">
                AND ${report2yAxis} = #{report2SelectedValue, jdbcType=VARCHAR}
            </if>
            <include refid="TEXFilter">
                <property name="includeReportDate" value="Y"/>
            </include>
        </where>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report3ResultMap" type="com.scp.customer.bean.TEXReport3Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <sql id="calendarDateCol">
        <choose>
            <when test='reportViewType == "VIEW_BY_DAY".toString()'>
                CALENDAR_DATE
            </when>
            <when test='reportViewType == "VIEW_BY_WEEK".toString()'>
                CALENDAR_WEEK
            </when>
            <when test='reportViewType == "VIEW_BY_MONTH".toString()'>
                CALENDAR_MONTH
            </when>
            <when test='reportViewType == "VIEW_BY_QUARTER".toString()'>
                CALENDAR_QUARTER
            </when>
            <when test='reportViewType == "VIEW_BY_YEAR".toString()'>
                CALENDAR_YEAR
            </when>
            <otherwise>
                CALENDAR_DATE
            </otherwise>
        </choose>
    </sql>

    <sql id="texBase">
        WITH TEX_BASE AS (
            SELECT T.*,
                   TO_CHAR(SY.DATE$, 'yyyy-mm-dd')          AS CALENDAR_DATE,
                   SY.YEAR || SY.WEEK_NO                    AS CALENDAR_WEEK,
                   SY.YEAR || SY.MONTH                      AS CALENDAR_MONTH,
                   TO_CHAR(T.${dateColumn}, 'YYYY"Q"Q')     AS CALENDAR_QUARTER,
                   TO_CHAR(SY.YEAR)                         AS CALENDAR_YEAR
            FROM ${SCPA.TECH_EXPERT_ASSESSMENTS_V}  T
                     LEFT JOIN SY_CALENDAR SY on sy.DATE$ = T.${dateColumn} and
                                                 sy.NAME = 'National Holidays'
        )
    </sql>

    <select id="queryReport3" parameterType="java.util.Map" resultMap="report3ResultMap">
        <include refid="texBase"/>
        SELECT
            <include refid="calendarDateCol"/>  AS CALENDAR_DATE,
            NVL(${report3ViewType}, 'Others')   AS NAME,
            ROUND(${valueColumn}, 2)            AS VALUE
        FROM TEX_BASE T
        <where>
            <include refid="TEXFilter">
                <property name="includeReportDate" value="Y"/>
            </include>
        </where>
        GROUP BY
            <include refid="calendarDateCol"/>,
            NVL(${report3ViewType}, 'Others')
        ORDER BY NVL(${report3ViewType}, 'Others')
    </select>

    <sql id="report3DetailsSQL">
        <include refid="texBase"/>
        SELECT * FROM TEX_BASE T
        <where>
            <include refid="calendarDateCol"/> = #{report3SelectedDate, jdbcType=VARCHAR}
            <if test="report3SelectedSeriesName != ''.toString() and report3SelectedSeriesName != null">
                AND ${report3ViewType} = #{report3SelectedSeriesName, jdbcType=VARCHAR}
            </if>
            <include refid="TEXFilter">
                <property name="includeReportDate" value="Y"/>
            </include>
        </where>

    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3TotalLine" parameterType="java.util.Map" resultType="BigDecimal">
        <include refid="texBase"/>
        SELECT ${valueColumn}
        FROM TEX_BASE T
        <where>
            CALENDAR_MONTH != TO_CHAR(SYSDATE, 'YYYYMM')
            <include refid="TEXFilter">
                <property name="includeReportDate" value="Y"/>
            </include>
        </where>
    </select>

    <sql id="report4SQL">
        <include refid="texBase"/>,
        TEMP AS (
            SELECT * FROM TEX_BASE T
            <where>
                <include refid="TEXFilter">
                    <property name="includeReportDate" value="Y"/>
                </include>
            </where>
        ),
        BASE AS (
                    SELECT
                        <foreach collection="report4SelectedColumns" item="item">
                                NVL(${item}, 'Others')               AS ${item},
                        </foreach>
                        NVL(<include refid="calendarDateCol"/>, 'Others')  AS CALENDAR_DATE,
                        NVL(ROUND(${valueColumn}, 3), 0)                   AS TOTAL
                    FROM TEMP t
                    GROUP BY
                        <foreach collection="report4SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        <include refid="calendarDateCol"/>
                    UNION ALL
                    SELECT <foreach collection="report4SelectedColumns" item="item">
                               NVL(${item}, 'Others')         AS ${item},
                           </foreach>
                           'Total',
                           NVL(ROUND(${valueColumn}, 3), 0)   AS TOTAL
                           FROM TEMP T
                           GROUP BY <foreach collection="report4SelectedColumns" item="item" separator=",">
                                       NVL(${item}, 'Others')
                                    </foreach>)
        SELECT * FROM BASE MM
            PIVOT (
                SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                    IN (
                    <foreach collection="report4ColumnNames" separator="," item="item">
                        '${item}'
                    </foreach>)
                  )
        ORDER BY
        <foreach collection="report4SelectedColumns" item="item" separator=",">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
    </sql>

    <select id="queryReport4Columns" resultType="java.lang.String">
        <include refid="texBase"/>
        SELECT * FROM (SELECT DISTINCT <include refid="calendarDateCol"/> AS CALENDAR
                        FROM TEX_BASE t
                        <where>
                            <include refid="TEXFilter">
                                <property name="includeReportDate" value="Y"/>
                            </include>
                        </where>
                        UNION SELECT 'Total' from dual
                        )
        ORDER BY CALENDAR DESC
        OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report4DetailsSQL">
        <include refid="texBase"/>
        SELECT * FROM TEX_BASE T
        <where>
            <if test="report4SelectedDate != null and report4SelectedDate != ''.toString() and report4SelectedDate != 'Total'.toString()">
                <include refid="calendarDateCol"/> = #{report4SelectedDate, jdbcType=VARCHAR}
            </if>
            <include refid="TEXFilter">
                <property name="includeReportDate" value="Y"/>
            </include>
            <foreach collection="report4SelectedColumns" item="item" index="index">
                <if test="report4SelectedValues[index] != null and report4SelectedValues[index] != ''.toString()">
                    <if test="report4SelectedValues[index] == 'Others'">
                        AND t.${item} IS NULL
                    </if>
                    <if test="report4SelectedValues[index] != 'Others'">
                        AND t.${item} = #{report4SelectedValues[${index}], jdbcType=VARCHAR}
                    </if>
                </if>
            </foreach>
        </where>
    </sql>

    <select id="queryReport4DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>


    <select id="queryReport5" resultType="java.util.Map">
        <include refid="texBase"/>,
        RESULT1_BASE as (
            SELECT COUNT(1) AS TOTAL_TEX,
                   SUM(DECODE(STATUS, 'Closed', 1, 0)) AS TEX_CLOSED_QTY,
                   SUM(CASE WHEN STATUS IN ('Cancelled', 'Closed') THEN 0 ELSE 1 END) AS OPEN_TEX
            FROM TEX_BASE T
            <where>
                <include refid="TEXFilter">
                    <property name="includeReportDate" value="Y"/>
                    <property name="dateColumn" value="CASE_CREATED_DATE"/>
                </include>
            </where>),
        RESULT2_BASE as (
            SELECT AVG(DECODE(STATUS, 'Closed', OVERALL_CYCLE_TIME, NULL)) AS AVG_OCT
            FROM TEX_BASE T
            <where>
                <include refid="TEXFilter">
                    <property name="includeReportDate" value="Y"/>
                </include>
            </where>)
        SELECT * FROM RESULT1_BASE LEFT JOIN RESULT2_BASE ON 1 = 1
    </select>

    <sql id="queryReport5DetailsSQL">
        <include refid="texBase"/>
        SELECT *
        FROM TEX_BASE T
        <where>
            <include refid="TEXFilter">
                <property name="includeReportDate" value="Y"/>
            </include>
        </where>
    </sql>

    <select id="queryReport5DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport5Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report6ResultMap" type="com.scp.customer.bean.TEXReport6Bean">
        <result property="XAXIS" column="XAXIS"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <sql id="report6CalendarDateCol">
        <choose>
            <when test='report6DateViewType == "VIEW_BY_DAY".toString()'>
                CALENDAR_DATE
            </when>
            <when test='report6DateViewType == "VIEW_BY_WEEK".toString()'>
                CALENDAR_WEEK
            </when>
            <when test='report6DateViewType == "VIEW_BY_MONTH".toString()'>
                CALENDAR_MONTH
            </when>
            <when test='report6DateViewType == "VIEW_BY_QUARTER".toString()'>
                CALENDAR_QUARTER
            </when>
            <when test='report6DateViewType == "VIEW_BY_YEAR".toString()'>
                CALENDAR_YEAR
            </when>
            <otherwise>
                CALENDAR_DATE
            </otherwise>
        </choose>
    </sql>

    <select id="queryReport6" parameterType="java.util.Map" resultMap="report6ResultMap">
        <include refid="texBase"/>,
        TEMP AS (SELECT T.*,
                        <include refid="report6CalendarDateCol"/> AS NAME
                 FROM TEX_BASE T
                 <where>
                     <include refid="TEXFilter">
                         <property name="includeReportDate" value="0"/>
                     </include>
                     AND ${dateColumn} BETWEEN TO_DATE(#{report6DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                     AND TO_DATE(#{report6DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                     AND STATUS = 'Closed'
                 </where>)
        SELECT 'Case To TEX' AS XAXIS, NAME, ROUND(NVL(AVG(CASE_TO_TEX), 0), 2) AS VALUE
        FROM TEMP T
        GROUP BY NAME

        UNION
        SELECT 'Open To Start' AS XAXIS, NAME, ROUND(NVL(AVG(OPEN_TO_START), 0), 2) AS VALUE
        FROM TEMP T
        GROUP BY NAME

        UNION
        SELECT 'Start To Complete' AS XAXIS, NAME, ROUND(NVL(AVG(START_TO_COMPLETE), 0), 2) AS VALUE
        FROM TEMP T
        GROUP BY NAME

        UNION
        SELECT 'Complete To Review' AS XAXIS, NAME, ROUND(NVL(AVG(COMPLETE_TO_REVIEW), 0), 2) AS VALUE
        FROM TEMP T
        GROUP BY NAME

        UNION
        SELECT 'Review To Delivery' AS XAXIS, NAME, ROUND(NVL(AVG(REVIEW_TO_DELIVERY), 0), 2) AS VALUE
        FROM TEMP T
        GROUP BY NAME

        UNION
        SELECT 'Delivery To Close' AS XAXIS, NAME, ROUND(NVL(AVG(DELIVERY_TO_CLOSE), 0), 2) AS VALUE
        FROM TEMP T
        GROUP BY NAME
    </select>
</mapper>
