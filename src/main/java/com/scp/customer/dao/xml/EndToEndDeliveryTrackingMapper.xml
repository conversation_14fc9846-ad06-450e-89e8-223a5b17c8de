<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IEndToEndDeliveryTrackingDao">

	<sql id="e2eFilter">
		AND T.CREATE_DATE IS NOT NULL
		<if test="_filters != null and _filters != ''.toString()">
			AND ${_filters}
		</if>
		<if test="treePathFilter != null and treePathFilter != ''.toString()">
			and ${treePathFilter}
		</if>
	</sql>

	<select id="queryCascader" resultType="java.util.Map">
		SELECT * FROM E2E_D0_DELIVERY_FILTER_V ORDER BY CATEGORY, DECODE(NAME,'Others','zzz',NAME)
	</select>


	<resultMap id="report1ResultMap" type="com.scp.customer.bean.EndToEndDeliveryTrackingReport1Bean">
		<result property="category1" column="CATEGORY1"/>
		<result property="category2" column="CATEGORY2"/>
		<result property="category3" column="CATEGORY3"/>
		<result property="category4" column="CATEGORY4"/>
		<result property="category5" column="CATEGORY5"/>
		<result property="value" column="value"/>
		<association property="tooltips" javaType="com.scp.customer.bean.EndToEndDeliveryTrackingReport1Tooltips">
			<result property="COUNT" column="COUNT"/>
		</association>
	</resultMap>

	<select id="queryReport1" resultMap="report1ResultMap">
		WITH BASE AS (SELECT * FROM ${SCPA.E2E_D0_DELIVERY_V})
		SELECT NVL(${level1}, 'Others') AS CATEGORY1,
			   NVL(${level2}, 'Others') AS CATEGORY2,
			   NVL(${level3}, 'Others') AS CATEGORY3,
		       <if test="level4 != null and level4 != ''.toString()">
		       	NVL(${level4}, 'Others') AS CATEGORY4,
		       </if>
		       <if test="level5 != null and level5 != ''.toString()">
			       NVL(${level5}, 'Others') AS CATEGORY5,
		       </if>
		       ${valueColumn} AS VALUE
		       <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
		       		,${tooltipsColumns}
		       </if>
		FROM BASE T
		<where>
			<include refid="e2eFilter"/>
		</where>
		GROUP BY ${level1},
				 ${level2},
				 ${level3}
		<if test="level4 != null and level4 != ''.toString()">,${level4}</if>
		<if test="level5 != null and level5 != ''.toString()">,${level5}</if>
	</select>

	<select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT DELIVERY_NUMBER || '/' || DELIVERY_ITEM       AS DN_NUMBER,
		       SALES_ORDER_NUMBER || '/' || SALES_ORDER_ITEM AS SO_NUMBER,
		       MATERIAL,
		       SO_CREATED_DATE                               AS SO_CREATED_TIME,
		       CREATE_TIME                                   AS DN_CREATE_TIME,
		       FIRST_ENTRY_TIME                              AS DN_SEND_TIME,
		       PLANT_CODE,
		       PICKING_STATUS,
		       PACKING_STATUS,
		       GOODS_MOVEMENT                                AS DN_STATUS,
		       D0_EXECUTION_STATUS,
			   OPERATION_SCENARIO
		FROM ${SCPA.E2E_D0_DELIVERY_V} T
		<where>
			<include refid="e2eFilter"/>
		</where>
		ORDER BY D0_EXECUTION_STATUS DESC
	</select>


	<resultMap id="report3ResultMap" type="com.scp.customer.bean.EndToEndDeliveryTrackingReport3Bean">
		<result property="CALENDAR" column="CALENDAR"/>
		<result property="NAME" column="NAME"/>
		<result property="VALUE" column="VALUE"/>
	</resultMap>

	<select id="queryReport3" parameterType="java.util.Map" resultMap="report3ResultMap">
		SELECT
			${dateColumn} AS CALENDAR,
			NVL(${report3ViewType}, 'Others') AS NAME,
			ROUND(${valueColumn}, 2) AS VALUE
		FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
		<if test="report3ViewCalendar == 'SE Working Day'">
			INNER JOIN SCPA.SY_CALENDAR t1 on TO_DATE(t.CALENDAR_DATE, 'YYYY-MM-DD') = t1.DATE$ and t1.WORKING_DAY = 1
			AND NAME = 'National Holidays'
		</if>
		WHERE TO_DATE(T.CALENDAR_DATE, 'YYYY/MM/DD') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD')
			AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
		<include refid="e2eFilter"/>
		GROUP BY
			${dateColumn},
			NVL(${report3ViewType}, 'Others')
		ORDER BY NVL(T.${report3ViewType}, 'Others')
	</select>

	<sql id="report3DetailsSQL">
		SELECT * FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
		WHERE T.CALENDAR_DATE = #{report3SelectedXAxis, jdbcType=VARCHAR}
		<if test="report3SelectedValue != null and report3SelectedValue != ''.toString()">
			AND T.${report3ViewType} = #{report3SelectedValue, jdbcType=VARCHAR}
		</if>
		<include refid="e2eFilter"/>
	</sql>

	<select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="report3DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="report3DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryReport3Line" resultType="java.util.HashMap">
		SELECT *
		FROM (SELECT
					DECODE(SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail') THEN 1 ELSE 0 END), 0, 0,
						   SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime') THEN 1 ELSE 0 END) /
							SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail') THEN 1 ELSE 0 END)) AS RATIO,
					T.${dateColumn}    AS CALENDAR
				FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
		<if test="report3ViewCalendar == 'SE Working Day'">
			INNER JOIN SCPA.SY_CALENDAR t1 on TO_DATE(t.CALENDAR_DATE, 'YYYY-MM-DD') = t1.DATE$ and t1.WORKING_DAY = 1
			AND NAME = 'National Holidays'
		</if>
		<where>
			TO_DATE(T.CALENDAR_DATE, 'YYYY/MM/DD') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD')
				AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
			<include refid="e2eFilter"/>
		</where>
		GROUP BY T.${dateColumn}) PIVOT (SUM(ROUND(RATIO * 100, 1)) FOR CALENDAR IN
			<choose>
				<when test="xAxis == null or xAxis == {}">
					('')
				</when>
				<otherwise>
					<foreach collection="xAxis" item="item" separator="," open="(" close=")">
						'${item}'
					</foreach>
				</otherwise>
			</choose>
		)
	</select>

	<sql id="queryReport4Sql">
		SELECT /*+ parallel */
		${reportViewType},
		<foreach collection="category" separator="," item="item">
			nvl(${item},'Others') "${item}"
		</foreach>,
		COUNT(1) AS LINE_COUNT
		FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
		<where>
			T.CREATE_DATE between TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
			<include refid="e2eFilter"/>
		</where>
		GROUP BY
		${reportViewType}
		<foreach collection="category"  item="item">
			,nvl(${item},'Others')
		</foreach>
		ORDER BY
		${reportViewType} DESC
		<foreach collection="category" item="item">
			,nvl(${item},'Others')
		</foreach>
	</sql>

	<select id="queryReport4Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport4Sql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport4Sql"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="queryReport4DetailsSql">
		SELECT *
		FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
		<where>
			T.CREATE_DATE between TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
			<include refid="e2eFilter"/>
			<if test="selectedValue != null and selectedValue.isEmpty() == false">
				<foreach collection="totalColumns" separator=" and " item="item" index="index" open=" and ">
					<choose>
						<when test="selectedValue[index] == 'Others'.toString()">
							(${item} = 'Others' OR ${item} IS NULL)
						</when>
						<when test="selectedValue[index] != null and selectedValue[index] != ''.toString()">
							${item} = #{selectedValue[${index}], jdbcType=VARCHAR}
						</when>
					</choose>
				</foreach>
			</if>
		</where>
	</sql>


	<select id="queryReport4DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport4DetailsSql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport4Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport4DetailsSql"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryReport5" resultType="java.util.Map">
		WITH WK AS (SELECT DATE$,
						   WORKING_DAY,
						   YEAR,
						   MONTH,
						   SUM(WORKING_DAY) OVER (ORDER BY DATE$) AS ROW_NUM
					FROM SCPA.SY_CALENDAR
					WHERE NAME = 'DC Working Calendar'),
		TEMP AS (SELECT *
				 FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
					<where>
						TRUNC(CREATE_DATE, 'DD') IN ((SELECT WK2.DATE$
														FROM DUAL T
														LEFT JOIN WK WK1 ON TRUNC(SYSDATE, 'DD') = WK1.DATE$
														LEFT JOIN WK WK2 ON WK1.ROW_NUM - 1 = WK2.ROW_NUM AND WK2.WORKING_DAY = 1),
														TRUNC(SYSDATE, 'DD'))
						<include refid="e2eFilter"/>
					</where>
				),
		YESTERDAY_D0 AS (SELECT *
						 FROM TEMP
						 WHERE TRUNC(CREATE_DATE, 'DD') != TRUNC(SYSDATE, 'DD')),
		TODAY_D0 AS (SELECT *
					 FROM TEMP
					 WHERE TRUNC(CREATE_DATE, 'DD') = TRUNC(SYSDATE, 'DD'))
		SELECT *
		FROM (SELECT SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail') THEN 1 ELSE 0 END)	AS YESTERDAY_TOTAL_D0_LINES,
					 SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime') THEN 1 ELSE 0 END)			AS YESTERDAY_COMPLETED_D0_LINES,
					DECODE(SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail') THEN 1 ELSE 0 END), 0, 0,
						   SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime') THEN 1 ELSE 0 END) /
						   SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail') THEN 1 ELSE 0 END))	AS YESTERDAY_D0_EXECUTION_RATIO
				FROM YESTERDAY_D0)
			LEFT JOIN (SELECT	COUNT(1)                                              											AS TODAY_TOTAL_D0_LINES,
								SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing') THEN 1 ELSE 0 END) 	AS TODAY_TARGET_D0_LINES,
				               	CASE WHEN COUNT(1) = 0 THEN 0 ELSE SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing') THEN 1 ELSE 0 END)
													/  COUNT(1) END																AS TODAY_TARGET_D0_RATIO,
								SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime') THEN 1 ELSE 0 END)						AS TODAY_COMPLETED_D0_LINES,
								DECODE(SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing') THEN 1 ELSE 0 END), 0, 0,
										SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime') THEN 1 ELSE 0 END) /
										SUM(CASE WHEN NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing') THEN 1 ELSE 0 END))	AS TODAY_D0_EXECUTION_RATIO
							  FROM TODAY_D0)
			    ON 1 = 1
	</select>

	<sql id="queryReport5DetailsSQL">
		WITH WK AS (SELECT DATE$,
							WORKING_DAY,
							YEAR,
							MONTH,
							SUM(WORKING_DAY) OVER (ORDER BY DATE$) AS ROW_NUM
							FROM SCPA.SY_CALENDAR
							WHERE NAME = 'DC Working Calendar')
		SELECT *
		FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
		<where>
			<include refid="e2eFilter"/>
			<choose>
				<when test="report5DetailsType == 'YESTERDAY_TOTAL_D0_LINES'.toString()">
					AND NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing')
					AND TRUNC(CREATE_DATE, 'DD') = (SELECT WK2.DATE$
													FROM DUAL T
													LEFT JOIN WK WK1 ON TRUNC(SYSDATE, 'DD') = WK1.DATE$
													LEFT JOIN WK WK2 ON WK1.ROW_NUM - 1 = WK2.ROW_NUM AND WK2.WORKING_DAY = 1)
				</when>
				<when test="report5DetailsType == 'YESTERDAY_COMPLETED_D0_LINES'.toString()">
					AND NVL(ONTIME_STATUS, 'Others') IN ('OnTime')
					AND TRUNC(CREATE_DATE, 'DD') = (SELECT WK2.DATE$
													FROM DUAL T
													LEFT JOIN WK WK1 ON TRUNC(SYSDATE, 'DD') = WK1.DATE$
													LEFT JOIN WK WK2 ON WK1.ROW_NUM - 1 = WK2.ROW_NUM AND WK2.WORKING_DAY = 1)
				</when>
				<when test="report5DetailsType == 'YESTERDAY_D0_EXECUTION_RATIO'.toString()">
					AND NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing')
					AND TRUNC(CREATE_DATE, 'DD') = (SELECT WK2.DATE$
													FROM DUAL T
													LEFT JOIN WK WK1 ON TRUNC(SYSDATE, 'DD') = WK1.DATE$
													LEFT JOIN WK WK2 ON WK1.ROW_NUM - 1 = WK2.ROW_NUM AND WK2.WORKING_DAY = 1)
				</when>
				<when test="report5DetailsType == 'TODAY_TOTAL_D0_LINES'.toString()">
					AND TRUNC(CREATE_DATE, 'DD') = TRUNC(SYSDATE, 'DD')
				</when>
				<when test="report5DetailsType == 'TODAY_TARGET_D0_LINES'.toString()">
					AND NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing')
					AND TRUNC(CREATE_DATE, 'DD') = TRUNC(SYSDATE, 'DD')
				</when>
				<when test="report5DetailsType == 'TODAY_TARGET_D0_RATIO'.toString()">
					AND NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing')
					AND TRUNC(CREATE_DATE, 'DD') = TRUNC(SYSDATE, 'DD')
				</when>
				<when test="report5DetailsType == 'TODAY_COMPLETED_D0_LINES'.toString()">
					AND NVL(ONTIME_STATUS, 'Others') IN ('OnTime')
					AND TRUNC(CREATE_DATE, 'DD') = TRUNC(SYSDATE, 'DD')
				</when>
				<when test="report5DetailsType == 'TODAY_D0_EXECUTION_RATIO'.toString()">
					AND NVL(ONTIME_STATUS, 'Others') IN ('OnTime', 'Fail', 'Ongoing')
					AND TRUNC(CREATE_DATE, 'DD') = TRUNC(SYSDATE, 'DD')
				</when>
			</choose>
		</where>
	</sql>

	<select id="queryReport5DetailsCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport5DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport5Details" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport5DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryConfigurationCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="configurationSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryConfiguration" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="configurationSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="configurationSQL">
		SELECT T.*
		FROM ${SCPA.MR3_D0_DC_OWNER} T
		ORDER BY PLANT_CODE
	</sql>

	<select id="queryPageAdmin" resultType="java.lang.String">
		select AUTH_DETAILS
		from SY_MENU_AUTH t
		where t.USER_ID = #{userid, jdbcType=VARCHAR} and t.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
	</select>

	<insert id="createConfigurationByTable">
		insert into MR3_D0_DC_OWNER
		(<foreach collection="headers" item="header" separator=",">
			${header}
		</foreach>, create_by$, create_date$
		)
		<foreach collection="creates" item="list" separator=" union all ">
			select <foreach collection="headers" item="header" separator=",">
				   		#{list.${header}, jdbcType=VARCHAR}
				   </foreach>, #{userid,jdbcType=VARCHAR}, sysdate
			FROM DUAL T
		</foreach>
	</insert>

	<delete id="deleteConfigurationByTable">
		delete from MR3_D0_DC_OWNER where PLANT_CODE in
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
		<if test="isAdmin == false">
			AND 0 = 1
		</if>
	</delete>

	<update id="updateConfigurationByTable">
		update MR3_D0_DC_OWNER
		SET
		<foreach collection="updates" item="col" separator=",">
			${col.key} = #{col.value,jdbcType=VARCHAR}
		</foreach>,
		update_by$ = #{userid,jdbcType=VARCHAR},
		update_date$ = sysdate
		where PLANT_CODE = #{plantCode,jdbcType=VARCHAR}
		<if test="isAdmin == false">
			AND 0 = 1
		</if>
	</update>

	<select id="queryCotConfigurationCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="cotConfigurationSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryCotConfiguration" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="cotConfigurationSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="cotConfigurationSQL">
		SELECT T.*, ROWIDTOCHAR(t.rowid) ROW_ID
		FROM ${SCPA.MR3_DELIVERY_DC_COT} T
		ORDER BY PLANT_CODE
	</sql>

	<insert id="createCotConfigurationByTable">
		insert into MR3_DELIVERY_DC_COT
		(<foreach collection="headers" item="header" separator=",">
			${header}
		</foreach>, create_by$, create_date$
		)
		<foreach collection="creates" item="list" separator=" union all ">
			select <foreach collection="headers" item="header" separator=",">
				   		#{list.${header}, jdbcType=VARCHAR}
				   </foreach>, #{userid,jdbcType=VARCHAR}, sysdate
			FROM DUAL T
		</foreach>
	</insert>

	<delete id="deleteCotConfigurationByTable">
		delete from MR3_DELIVERY_DC_COT where ROWID in
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
		<if test="isAdmin == false">
			AND 0 = 1
		</if>
	</delete>

	<update id="updateCotConfigurationByTable">
		UPDATE MR3_DELIVERY_DC_COT
		SET
		<foreach collection="updates" item="col" separator=",">
			${col.key} = #{col.value,jdbcType=VARCHAR}
		</foreach>,
		update_by$ = #{userid,jdbcType=VARCHAR},
		update_date$ = sysdate
		where PLANT_CODE || ',' || SHIP_TO_CITY = #{rowId}
		<if test="isAdmin == false">
			AND 0 = 1
		</if>
	</update>

	<sql id="rca_filter">
		<if test="_filters != null and _filters != ''.toString()">
			and ${_filters}
		</if>
	</sql>

	<sql id="queryRCAWeeklySQL">
		WITH RCA_WEEKLY AS (
			SELECT T.*, NVL(T2.RCA_RESULT,T3.RCA_RESULT) AS RCA_RESULT
			FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
			LEFT JOIN E2E_D0_DELIVERY_RCA_FEEDBACK T2 ON T.DELIVERY_NUMBER = T2.DELIVERY_NUMBER AND T.DELIVERY_ITEM = T2.DELIVERY_ITEM AND T2.RCA_TYPE = 'W' AND T.ONTIME_STATUS = 'Fail'
			LEFT JOIN E2E_D0_DELIVERY_RCA_FEEDBACK T3 ON T.DELIVERY_NUMBER = T3.DELIVERY_NUMBER AND T.DELIVERY_ITEM = T3.DELIVERY_ITEM AND T3.RCA_TYPE = 'D' AND T.ONTIME_STATUS = 'Fail'
			WHERE T.CALENDAR_WEEK BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
				AND T.ONTIME_STATUS IS NOT NULL
				<include refid="rca_filter"/>
		),
		RCA_YEARLY as (
			SELECT T.*, NVL(T2.RCA_RESULT,T3.RCA_RESULT) RCA_RESULT
			FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
			LEFT JOIN E2E_D0_DELIVERY_RCA_FEEDBACK T2 ON T.DELIVERY_NUMBER = T2.DELIVERY_NUMBER AND T.DELIVERY_ITEM = T2.DELIVERY_ITEM AND T2.RCA_TYPE = 'W' AND T.ONTIME_STATUS = 'Fail'
			LEFT JOIN E2E_D0_DELIVERY_RCA_FEEDBACK T3 ON T.DELIVERY_NUMBER = T3.DELIVERY_NUMBER AND T.DELIVERY_ITEM = T3.DELIVERY_ITEM AND T3.RCA_TYPE = 'D' AND T.ONTIME_STATUS = 'Fail'
			WHERE T.CALENDAR_WEEK BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
				AND T.ONTIME_STATUS IS NOT NULL
				<include refid="rca_filter"/>
		)
		SELECT <foreach collection="field" item="item">
					tt.${item},
				</foreach>
				<if test="field.contains('PLANT_CODE')">
					tt.order_field,
				</if>
				<foreach collection="weeks" separator="," item="item">
					mm."'${item}'_FAIL",
					mm."'${item}'_ONTIME",
					mm."'${item}'_RATIO",
					mm."'${item}'_CONFIRMED"
				</foreach>
				<foreach collection="months" separator="," item="item" open=",">
					nn."'${item}'_FAIL",
					nn."'${item}'_ONTIME",
					nn."'${item}'_RATIO"
				</foreach>
				<foreach collection="years" separator="," item="item" open=",">
					tt."'${item}'_FAIL",
					tt."'${item}'_ONTIME",
					tt."'${item}'_RATIO"
				</foreach>
		from (
			select *
			from (select
						<foreach collection="field" item="item">
							nvl(t.${item}, 'Others')             ${item},
						</foreach>
						<if test="field.contains('PLANT_CODE')">
							nvl(max(t.PLANT_CODE), 'Others')    order_field,
						</if>
						COUNT(DECODE(T.ONTIME_STATUS, 'Fail', 1, null)) 							fail,
						COUNT(DECODE(T.ONTIME_STATUS, 'OnTime', 1, null)) 							ontime,
						to_char(TO_DATE(T.CALENDAR_DATE, 'YYYY-MM-DD'), 'yyyy"YTD"')      	years
						from RCA_YEARLY t
						group by
						<foreach collection="field" item="item">
							t.${item},
						</foreach>
						to_char(TO_DATE(T.CALENDAR_DATE, 'YYYY-MM-DD'), 'yyyy"YTD"')) mm
			PIVOT 	(
					sum(ontime) ontime,
					sum(fail) fail,
					sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR years
					IN (
							<foreach collection="years" separator="," item="item">
								'${item}'
							</foreach>
						)
					)
		) TT
		LEFT JOIN
			(
			SELECT *
			FROM (SELECT
					<foreach collection="field" item="item">
						nvl(t.${item}, 'Others')             ${item},
					</foreach>
					<if test="field.contains('PLANT_CODE')">
						nvl(max(t.PLANT_CODE), 'Others')    order_field,
					</if>
					COUNT(DECODE(T.ONTIME_STATUS, 'Fail', 1, null)) fail,
					COUNT(DECODE(T.ONTIME_STATUS, 'OnTime', 1, null)) ontime,
					COUNT(T.RCA_RESULT) confirmed,
					SUBSTR(CALENDAR_WEEK, 3, 2) || 'W' || SUBSTR(CALENDAR_WEEK, 5, 2) WEEKS
					from RCA_WEEKLY t
					group by
					<foreach collection="field" item="item">
						t.${item},
					</foreach>
					SUBSTR(CALENDAR_WEEK, 3, 2) || 'W' || SUBSTR(CALENDAR_WEEK, 5, 2)) mm
			PIVOT (
					sum(ontime) ontime,
					sum(fail) fail,
					sum(confirmed) confirmed,
					sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR weeks
					IN (
					<foreach collection="weeks" separator="," item="item">
						'${item}'
					</foreach>
					)
			)
			) mm on
		<foreach collection="field" item="item" separator="and">
			mm.${item} = tt.${item}
		</foreach>

		LEFT JOIN
			(
			SELECT *
			FROM (SELECT
					<foreach collection="field" item="item">
						nvl(t.${item}, 'Others')             ${item},
					</foreach>
					<if test="field.contains('PLANT_CODE')">
						nvl(max(t.PLANT_CODE), 'Others')    order_field,
					</if>
					COUNT(DECODE(T.ONTIME_STATUS, 'Fail', 1, null))                      fail,
					COUNT(DECODE(T.ONTIME_STATUS, 'OnTime', 1, null))                    ontime,
					to_char(TO_DATE(T.CALENDAR_DATE, 'YYYY-MM-DD'), 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') months
			FROM RCA_WEEKLY T
			GROUP BY
					<foreach collection="field" item="item">
						t.${item},
					</foreach> to_char(TO_DATE(T.CALENDAR_DATE, 'YYYY-MM-DD'), 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN')) mm
			PIVOT (
						sum(ontime) ontime,
						sum(fail) fail,
						sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR months
						IN (
							<foreach collection="months" separator="," item="item">
								'${item}'
							</foreach>
							)
				)
			) NN ON
			<foreach collection="field" item="item" separator="and">
				nn.${item} = tt.${item}
			</foreach>
	</sql>

	<select id="queryRCAWeekly" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="queryRCAWeeklySQL"/>
		order by
		<if test="_page.sort != null and _page.sort != ''.toString()">
			${_page.sort},
		</if>
		"'${defaultOrderColumn}'_FAIL" + "'${defaultOrderColumn}'_ONTIME" DESC
		OFFSET 0 ROWS FETCH NEXT 65535 ROWS ONLY
	</select>

	<select id="queryRCACode" resultType="java.lang.String">
		SELECT RCA_CODE FROM SCPA.E2E_D0_DELIVERY_RCA_CODE
	</select>

	<select id="queryWeekOpts" resultType="java.lang.String">
		SELECT distinct CALENDAR_WEEK FROM SCPA.E2E_D0_DELIVERY_HIST_V WHERE NVL(DATA_SOUCRE,'Others') != 'OLD MA' ORDER BY CALENDAR_WEEK DESC
	</select>

	<insert id="saveRCAResult" parameterType="java.util.Map">
		MERGE INTO SCPA.E2E_D0_DELIVERY_RCA_FEEDBACK T
		USING
		(
		<foreach collection="dataList" item="item" separator="union all">
			SELECT #{item.order, jdbcType=VARCHAR} DELIVERY_NUMBER,
			#{item.item, jdbcType=VARCHAR} DELIVERY_ITEM,
			#{item.rca_result, jdbcType=VARCHAR} RCA_RESULT,
			#{item.rca_comments, jdbcType=VARCHAR} RCA_COMMENTS,
			#{item.type, jdbcType=VARCHAR} RCA_TYPE
			from dual
		</foreach>
		) S ON (T.DELIVERY_NUMBER = S.DELIVERY_NUMBER AND T.DELIVERY_ITEM = S.DELIVERY_ITEM)
		WHEN MATCHED THEn
		UPDATE SET
		T.RCA_RESULT = S.RCA_RESULT,
		T.RCA_COMMENTS = S.RCA_COMMents,
		T.RCA_TYPE = S.RCA_TYPE,
		T.UPDATE_DATE$ = SYSDATE,
		t.update_by$ = #{session.userid, jdbcType=VARCHAR}
		WHEN NOT MATCHED THEN
		INSERT (DELIVERY_NUMBER, DELIVERY_ITEM, RCA_RESULT, RCA_COMMENTS, CREATE_BY$, CREATE_DATE$, RCA_TYPE)
		VALUES (S.delivery_number, s.delivery_item, s.rca_result, s.rca_comments, #{session.userid, jdbcType=VARCHAR}, sysdate, s.rca_type)
	</insert>

	<sql id="queryRCAWeeklyDetailsSql">
		SELECT NVL(T4.RCA_RESULT, T3.RCA_RESULT)        	AS	 "RCA_RESULT",
			   T2.RCA_CODE                                  AS   "RCA_TIPS",
			   T2.RECOM_RCA_CODE                            AS   "RECOM_RCA_CODE",
			   NVL(T4.RCA_COMMENTS, T3.RCA_COMMENTS)        AS   "RCA_COMMENTS",
			   T2.DESCRIPTION                               AS   "RCA_REMARK",
			   T.*
		FROM ${SCPA.E2E_D0_DELIVERY_HIST_V} T
		LEFT JOIN E2E_D0_DELIVERY_RCA_V T2 ON T.DELIVERY_NUMBER = T2.DELIVERY_NUMBER AND
											  T.DELIVERY_ITEM = T2.DELIVERY_ITEM AND
											  SUBSTR(T.CALENDAR_WEEK, 3, 2) || 'W' || SUBSTR(T.CALENDAR_WEEK, 5, 2) = T2.WEEK
		LEFT JOIN E2E_D0_DELIVERY_RCA_FEEDBACK T3
			ON T.DELIVERY_NUMBER = T3.DELIVERY_NUMBER AND T.DELIVERY_ITEM = T3.DELIVERY_ITEM  AND T3.RCA_TYPE = 'D'
		LEFT JOIN E2E_D0_DELIVERY_RCA_FEEDBACK T4
			ON T.DELIVERY_NUMBER = T4.DELIVERY_NUMBER AND T.DELIVERY_ITEM = T4.DELIVERY_ITEM AND T4.RCA_TYPE = 'W'
		WHERE
		<choose>
			<when test="selectedDateType == 'month'.toString()">
				TO_CHAR(TO_DATE(T.CALENDAR_DATE, 'YYYY-MM-DD'), 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') = #{selectedDate, jdbcType=VARCHAR}
				and T.CALENDAR_WEEK BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
			</when>
			<when test="selectedDateType == 'week'.toString()">
				SUBSTR(CALENDAR_WEEK, 3, 2) || 'W' || SUBSTR(CALENDAR_WEEK, 5, 2) = #{selectedDate, jdbcType=VARCHAR}
				and T.CALENDAR_WEEK BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
			</when>
			<when test="selectedDateType == 'year'.toString()">
				CALENDAR_YEAR = #{selectedDate, jdbcType=VARCHAR}
				and T.CALENDAR_WEEK BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
			</when>
			<when test="selectedDateType == 'all'.toString()">
				T.CALENDAR_WEEK BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
			</when>
			<when test="selectedDateType == 'weekly'.toString()">
				T.CALENDAR_WEEK BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
			</when>
			<otherwise> 1 = 1 </otherwise>
		</choose>
		<if test="selectedType == 'On Time'.toString()">
			and t.ONTIME_STATUS = 'OnTime'
		</if>
		<if test="selectedType == 'Delay'.toString()">
			and t.ONTIME_STATUS = 'Fail'
		</if>
		<include refid="rca_filter"/>
		<foreach collection="field" item="item" index="index">
			<choose>
				<when test="selectedField[index] == 'Others'.toString()">
					and (${item} is null or ${item} = 'Others')
				</when>
				<otherwise>
					<if test="selectedField[index] != 'Total'.toString() and selectedField[index] != null">
						and ${item} = #{selectedField[${index}], jdbcType=VARCHAR}
					</if>
				</otherwise>
			</choose>
		</foreach>
	</sql>

	<select id="queryRCAWeeklyDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryRCAWeeklyDetailsSql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryRCAWeeklyDetails" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="global.select_header"/>
		<include refid="queryRCAWeeklyDetailsSql"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="downloadRCAWeeklyDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryRCAWeeklyDetailsSql"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="queryRCATipsSql">
		SELECT RCA_CODE AS RCA_TIPS_CODE, DESCRIPTION, NULL FROM SCPA.E2E_D0_DELIVERY_RCA_CODE
	</sql>

	<select id="queryRCATips" resultType="java.util.LinkedHashMap">
		<include refid="queryRCATipsSql"/>
	</select>

	<select id="queryRCATipsListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryRCATipsSql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryRCATipsList" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryRCATipsSql"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryRCAWeeklyWeekColumns" resultType="java.lang.String">
		SELECT WEEK_NO
		FROM (
			     SELECT DISTINCT SUBSTR(T.YEAR, 3 ,2) || 'W' || T.WEEK_NO WEEK_NO
			     FROM SY_CALENDAR T
			     WHERE T.NAME = 'National Holidays'
				   AND T.YEAR || t.WEEK_NO BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
		     ) T
		ORDER BY WEEK_NO DESC
	</select>

	<select id="queryRCAWeeklyMonthColumns" resultType="java.lang.String">
		SELECT MONTH
		FROM (
			     SELECT DISTINCT TO_CHAR(t.date$, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') MONTH, T.YEAR || t.MONTH AS MONTH_ORDER
			     FROM SY_CALENDAR T
			     WHERE T.NAME = 'National Holidays'
				   AND T.YEAR || t.WEEK_NO BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
					AND TRUNC(T.DATE$, 'MM') &lt;= TRUNC(SYSDATE, 'MM')
		     ) T
		ORDER BY MONTH_ORDER DESC
	</select>

	<select id="queryRCAWeeklyYearColumns" resultType="java.lang.String">
		SELECT T.YEAR
		FROM (
			     SELECT DISTINCT T.YEAR || 'YTD' YEAR
			     FROM SY_CALENDAR T
			     WHERE T.NAME = 'National Holidays'
				   AND T.YEAR || t.WEEK_NO BETWEEN #{rcaSelectedWeekRange[0], jdbcType=VARCHAR} AND #{rcaSelectedWeekRange[1], jdbcType=VARCHAR}
		     ) T
		ORDER BY T.YEAR DESC
	</select>


	<select id="queryManualD0Count" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="manualD0SQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryManualD0" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="manualD0SQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="manualD0SQL">
        SELECT  DISTINCT DELIVERY_NUMBER,
                UPLOAD_OWNER,
                UPLOAD_OWNER_NAME,
                CREATE_DATE$  AS CREATE_DATE,
                DELIVERY_NUMBER AS PRIMARY_KEY
        FROM ${SCPA.E2E_D0_MANUAL_DN_V} T
		<where>
			<choose>
				<when test="dateRange.size() > 0">
					TRUNC(T.CREATE_DATE$, 'DD') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD')
					AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
				</when>
				<otherwise>
					TRUNC(SYSDATE, 'DD') = TRUNC(T.CREATE_DATE$, 'DD')
				</otherwise>
			</choose>
			<if test="_filters != null and _filters != ''.toString()">
				AND ${_filters}
			</if>
		</where>
		ORDER BY T.CREATE_DATE$
	</sql>

	<select id="queryManualD0Cascader" resultType="java.util.Map">
		SELECT * FROM E2E_D0_MANUAL_DN_FILTER_V ORDER BY CATEGORY, DECODE(NAME,'Others','zzz',NAME)
	</select>

	<insert id="createManualD0">
		insert into MR3_E2E_D0_MANUAL_DN
		(<foreach collection="headers" item="header" separator=",">
			<choose>
				<when test="header == 'DELIVERY_NUMBER'.toString() or header == 'MANUAL_REASON'.toString()">
					${header}
				</when>
			</choose>
		 </foreach>, create_by$, create_date$
		)
		<foreach collection="creates" item="list" separator=" union all ">
			select <foreach collection="headers" item="header" separator=",">
			    		<choose>
						    <when test="header == 'DELIVERY_NUMBER'.toString() or header == 'MANUAL_REASON'.toString()">
							    LTRIM(#{list.${header}, jdbcType=VARCHAR}, '0') AS ${header}
						    </when>
					    </choose>
				   </foreach>
			, #{userid,jdbcType=VARCHAR}, sysdate
			FROM DUAL T
		</foreach>
	</insert>

	<delete id="deleteManualD0">
		delete from MR3_E2E_D0_MANUAL_DN where DELIVERY_NUMBER in
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
		<if test="isAdmin == false">
			AND 0 = 1
		</if>
	</delete>

    <select id="queryReport2AvailableColumns" resultType="java.lang.String">
        SELECT COLUMN_NAME
        FROM ALL_TAB_COLS
        WHERE TABLE_NAME = 'E2E_D0_MANUAL_DN_V'
          AND OWNER = 'SCPA'
        ORDER BY COLUMN_ID
    </select>

    <select id="queryManualD0DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="manualD0DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryManualD0Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="manualD0DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="manualD0DetailsSQL">
        SELECT T.*
        FROM ${SCPA.E2E_D0_MANUAL_DN_V} T
        <where>
            <choose>
                <when test="dateRange.size() > 0">
                    TRUNC(T.UPLOAD_DATE, 'DD') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD')
                    AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
                </when>
                <otherwise>
                    TRUNC(SYSDATE, 'DD') = TRUNC(T.UPLOAD_DATE, 'DD')
                </otherwise>
            </choose>
            <if test="_filters != null and _filters != ''.toString()">
                AND ${_filters}
            </if>
        </where>
        ORDER BY UPLOAD_DATE
    </sql>


    <select id="queryFeedbackExists" resultType="java.lang.Integer">
        select count(1) from SCPA.E2E_D0_MANUAL_DN_V
        WHERE DELIVERY_NUMBER = #{deliveryNumber,jdbcType=VARCHAR}
    </select>

    <update id="updateFeedback">
        UPDATE MR3_E2E_D0_MANUAL_DN
        SET
        <foreach collection="updates" item="col" separator=",">
            <if test="col.key == 'DC_FEEDBACK'.toString() or col.key == 'DC_COMMENTS'.toString()">
                ${col.key} = #{col.value,jdbcType=VARCHAR}
            </if>
        </foreach>,
        UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
        UPDATE_DATE$ = SYSDATE
        WHERE
        DELIVERY_NUMBER = #{deliveryNumber,jdbcType=VARCHAR}
    </update>
</mapper>
