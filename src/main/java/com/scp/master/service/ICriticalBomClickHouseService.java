package com.scp.master.service;

import com.starter.context.bean.Response;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * CriticalBom ClickHouse服务接口
 * 
 * <AUTHOR>
 */
public interface ICriticalBomClickHouseService {

    Response initPage(Map<String, Object> parameterMap, String userid);

    Response adminCheck(Map<String, Object> parameterMap, String userid);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap);

    void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport2(String userid, Map<String, Object> parameterMap);

    Response uploadReport2(String userid, MultipartFile file) throws Exception;

    void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response);

    Response querySankeyChart(Map<String, Object> parameterMap);
}
