<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.ICriticalBomCalculatorDao">
    <select id="queryUserBatchList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT BATCH_ID KEY,
			   NAME AS LABEL,
			   CASE WHEN T.START_TIME IS NULL THEN 'not start'
                    WHEN T.END_TIME IS NULL THEN 'processing'
                    WHEN T.END_TIME IS NOT NULL THEN 'finished' END AS SUB_LABEL,
			   GROUPS
		  FROM CB_BCDC_LOG T
		  <if test="authType != 'ADMIN'.toString()">
		 	where USERID = #{userid,jdbcType=VARCHAR}
              <if test="batch_id.size() != 0">
		 	    OR BATCH_ID IN
              <foreach collection="batch_id" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
              </if>
		  </if>
		 ORDER BY GROUPS, NAME
    </select>

    <select id="queryTaskInfo" resultType="java.util.Map">
        SELECT  T.GROUPS as CATEGORY,
                T.NAME || '@' || T.BATCH_ID AS NAME,
                T.BATCH_ID,
                T.PARAMS,
                T.STEP,
                T.STEP_MODE
        FROM CB_BCDC_LOG T
        WHERE T.GROUPS IS NOT NULL
        <if test="authType != 'ADMIN'.toString()">
            AND USERID = #{userid,jdbcType=VARCHAR}
        <if test="batch_id.size() != 0">
           OR BATCH_ID IN
            <foreach collection="batch_id" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        </if>
        </if>
        ORDER BY GROUPS,NAME
    </select>

    <select id="queryBatchInfo" resultType="java.util.Map">
        SELECT T2.SESA_CODE, T2.USER_NAME, T.NAME, T.WORK_MODE, T.START_TIME, T.END_TIME, T.PARAMS, T.STEP, T.STEP_MODE, T.MODULE
          FROM CB_BCDC_LOG T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USERID = T2.SESA_CODE
         WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </select>

    <select id="queryFilterList" resultType="java.util.Map">
        SELECT * FROM BCD_FILTER_V ORDER BY CATEGORY, DECODE(NAME,'Others','zzz', NAME)
    </select>

    <select id="initExistsGroup" resultType="java.lang.String">
        SELECT DISTINCT GROUPS
		  FROM CB_BCDC_LOG T
		 WHERE T.GROUPS IS NOT NULL
		  <if test="authType != 'ADMIN'.toString()">
		 	AND USERID = #{userid,jdbcType=VARCHAR}
		  </if>
		 ORDER BY GROUPS
    </select>

    <insert id="saveBestCanDoLog" parameterType="com.scp.simulation.bean.BestCanDoLog">
        INSERT INTO BCDC_EXECUTE_LOGS (BATCH_ID, TIME, MESSAGE)
        values
        (#{batch_id, jdbcType=VARCHAR}, TO_DATE(#{time, jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS'), #{message, jdbcType=VARCHAR})
    </insert>

    <select id="queryExecuteLogs" resultType="java.lang.String">
        SELECT TO_CHAR(TIME, 'YYYY/MM/DD HH24:MI:SS') || ' - ' || MESSAGE
          FROM BCDC_EXECUTE_LOGS T
         WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
         ORDER BY TIME
    </select>

    <insert id="insertLog" parameterType="java.util.Map">
        DECLARE
            PARAMS_COLB CLOB := #{params, jdbcType=CLOB};
        BEGIN
            INSERT INTO CB_BCDC_LOG (USERID, BATCH_ID, NAME, PARAMS, CREATE_DATE, WORK_MODE, GROUPS, STEP, STEP_MODE,MODULE)
            VALUES
            (#{userid, jdbcType=VARCHAR}, #{batch_id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR},
            PARAMS_COLB, SYSDATE, #{mode, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, #{step, jdbcType=VARCHAR}, #{step_mode, jdbcType=VARCHAR}, #{module, jdbcType=VARCHAR});
        END;
    </insert>

    <insert id="copySOData">
        INSERT INTO BCD_INPUT_PRIORITY
        (BATCH_ID, ORDER_NUMBER, PRIORITY_DATE, PLANT_CODE, MATERIAL, QTY, REQUEST_DATE)
        SELECT #{batch_id, jdbcType=VARCHAR},
               T.SALES_ORDER_NUMBER || '&amp;' || T.SALES_ORDER_ITEM,
               NULL,
               T.PLANT_CODE,
               T.MATERIAL,
               T.ORDER_QUANTITY,
               T.CALENDAR_DATE
        FROM DEMAND_BACKLOG_V T
       WHERE T.BOM_CATEGORY != 'HEADER'
         AND T.PLANT_SCOPE IN (
                    'PLANT_IG_DOMESTIC',
                    'PLANT_IG_EXPORT',
                    'PLANT_OG_DOMESTIC',
                    'PLANT_OG_EXPORT'
             )
         <if test="dateRange.size() > 0">
             AND T.CALENDAR_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
         </if>
         AND T.PLANT_CODE = #{plant, jdbcType=VARCHAR}
         <if test="priorityFilters != null and priorityFilters != ''.toString()">
             AND ${priorityFilters}
         </if>
    </insert>

    <insert id="copyMOData">
        INSERT INTO BCD_INPUT_PRIORITY
        (BATCH_ID, ORDER_NUMBER, PRIORITY_DATE, PLANT_CODE, MATERIAL, QTY, REQUEST_DATE)
        SELECT #{batch_id, jdbcType=VARCHAR},
               T.MO_NUMBER,
               NULL,
               T.PLANT_CODE,
               T.MATERIAL,
               T.OPEN_MO_QTY,
               T.MO_START_DATE
        FROM OPEN_MO_STRUCTURE_V T
       WHERE T.PLANT_CODE = #{plant, jdbcType=VARCHAR}
       <if test="dateRange.size() > 0">
             AND T.MO_START_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
       </if>
       <if test="priorityFilters != null and priorityFilters != ''.toString()">
           AND ${priorityFilters}
       </if>
    </insert>

    <insert id="copyPOData">
        INSERT INTO BCD_INPUT_PO
        (BATCH_ID, PLANT_CODE, MATERIAL, CONFIRM_CAT, ETA_DATE, QTY)
        SELECT #{batch_id, jdbcType=VARCHAR},
               T.PLANT_CODE,
               T.MATERIAL,
               T.CONFIRM_CAT,
               T.DUEDATE,
               T.OPEN_QTY
        FROM OPEN_PO_STRUCTURE_V T
       WHERE
        T.PLANT_CODE = #{plant, jdbcType=VARCHAR}
       <if test="poFilters != null and poFilters != ''.toString()">
           AND ${poFilters}
       </if>
    </insert>

    <sql id="queryStartSQL">
        SELECT CONCAT(MATERIAL, '|', PLANT_CODE) AS ROW_ID,
               MATERIAL,
               PLANT_CODE
        FROM scpc.CB_BCDC_DEMAND_START_CLICK T
        WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <sql id="queryReport1SQL">
        SELECT CONCAT(MATERIAL, '|', PRIMARY_KEY) AS ROW_ID,
               PRIMARY_KEY,
               PRIORITY,
               MATERIAL,
               QTY
          FROM BCDC_INPUT_DEMAND T
         WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT PRIMARY_KEY,
               MATERIAL,
               PRIORITY,
               QTY
          FROM BCDC_INPUT_DEMAND T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <select id="queryStartCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryStartSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryStart" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        SELECT * FROM (
        <include refid="queryStartSQL"/>
        ) PAGE
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ORDER BY ${_page.sort}
        </if>
        <if test="_page.pagging">
            LIMIT #{_page.length,jdbcType=INTEGER} OFFSET #{_page.start,jdbcType=INTEGER}
        </if>
        <if test="not _page.pagging">
            LIMIT #{_page.maxRows,jdbcType=INTEGER}
        </if>
    </select>

    <select id="downloadStart" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT
        MATERIAL,
        PLANT_CODE
        FROM CB_BCDC_DEMAND_START_CLICK T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport2SQL">
            SELECT CONCAT(MATERIAL, '|', BOM_COMPONENT) AS ROW_ID,
               MATERIAL,
               BOM_COMPONENT,
               "USAGE"
          FROM CB_BCDC_INPUT_BOM_CLICK T
         WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        SELECT * FROM (
        <include refid="queryReport2SQL"/>
        ) PAGE
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ORDER BY ${_page.sort}
        </if>
        <if test="_page.pagging">
            LIMIT #{_page.length,jdbcType=INTEGER} OFFSET #{_page.start,jdbcType=INTEGER}
        </if>
        <if test="not _page.pagging">
            LIMIT #{_page.maxRows,jdbcType=INTEGER}
        </if>
    </select>

    <select id="downloadReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT MATERIAL,
               BOM_COMPONENT,
               "USAGE"
          FROM CB_BCDC_INPUT_BOM_CLICK T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <insert id="insertBomReport2">
        INSERT INTO scpc.CB_BCDC_INPUT_BOM
        (BATCH_ID,
         MATERIAL,
         BOM_COMPONENT,
         USAGE
        )
            SELECT #{batch_id, jdbcType=VARCHAR},
                   T.MATERIAL,
                   T.BOM_COMPONENT,
                   T.COMPONENT_QTY
            FROM scpc.CRITICAL_BOM_V T
            WHERE
            T.PLANT_CODE = #{plant, jdbcType=VARCHAR}
    </insert>

    <insert id="insertRecourseReport3">
        INSERT INTO scpc.CB_BCDC_INPUT_RECOURSE
        (BATCH_ID,
         MATERIAL<foreach collection="stepIndexes" item="index" separator=",">,
         STEP${index}</foreach>
        )
        SELECT #{batch_id, jdbcType=VARCHAR},
               T.MATERIAL<foreach collection="stepIndexes" item="index" separator=",">,
               0</foreach>
        FROM scpc.CRITICAL_BOM_V T
        WHERE
            T.PLANT_CODE = #{plant, jdbcType=VARCHAR}
    </insert>

    <sql id="queryReport11SQL">
        SELECT ROWIDTOCHAR(ROWID) ROW_ID,
               FIX_RECOURSE
        FROM BCDC_FIX_RECOURSE T
        WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport11Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport11SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport11" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport11SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport11" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT FIX_RECOURSE
        FROM BCDC_FIX_RECOURSE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport3SQL">
        SELECT ROWIDTOCHAR(ROWID) ROW_ID,
               MATERIAL,
               STEP0 ,
               STEP1 ,
               STEP2 ,
               STEP3 ,
               STEP4 ,
               STEP5 ,
               STEP6 ,
               STEP7 ,
               STEP8 ,
               STEP9 ,
               STEP10,
               STEP11,
               STEP12,
               STEP13,
               STEP14,
               STEP15,
               STEP16,
               STEP17,
               STEP18,
               STEP19,
               STEP20,
               STEP21,
               STEP22,
               STEP23,
               STEP24,
               STEP25,
               STEP26,
               STEP27,
               STEP28,
               STEP29,
               STEP30,
               STEP31,
               STEP32,
               STEP33,
               STEP34,
               STEP35
        FROM BCDC_INPUT_RECOURSE T
        WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>



    <select id="queryReport3Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT MATERIAL,
        STEP0 ,
        STEP1 ,
        STEP2 ,
        STEP3 ,
        STEP4 ,
        STEP5 ,
        STEP6 ,
        STEP7 ,
        STEP8 ,
        STEP9 ,
        STEP10,
        STEP11,
        STEP12,
        STEP13,
        STEP14,
        STEP15,
        STEP16,
        STEP17,
        STEP18,
        STEP19,
        STEP20,
        STEP21,
        STEP22,
        STEP23,
        STEP24,
        STEP25,
        STEP26,
        STEP27,
        STEP28,
        STEP29,
        STEP30,
        STEP31,
        STEP32,
        STEP33,
        STEP34,
        STEP35
        FROM BCDC_INPUT_RECOURSE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport9SQL">
        SELECT ROWIDTOCHAR(ROWID) ROW_ID,
               MATERIAL AS BOM_COMPONENT,
               STEP0
        FROM BCDC_INPUT_RECOURSE T
        WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>



    <select id="queryReport9Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport9SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport9" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport9SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport9" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT MATERIAL AS BOM_COMPONENT,
        STEP0
        FROM BCDC_INPUT_RECOURSE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport7" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT MATERIAL_IN_BOM, FOLLOW_UP_MATERIAL
        FROM BCDC_INPUT_SUBSTITUTION T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <select id="queryBatchCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
          FROM CB_BCDC_LOG T
         WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR} AND T.USERID = #{userid, jdbcType=VARCHAR}
               AND NOT (T.START_TIME IS NOT NULL AND T.END_TIME IS NULL)
    </select>

    <select id="querySimulateCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
          FROM CB_BCDC_LOG T
         WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
           AND T.START_TIME IS NULL
    </select>

    <delete id="deleteBatch">
        BEGIN
            DELETE FROM CB_BCDC_LOG T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
            DELETE FROM BCDC_INPUT_RECOURSE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
            DELETE FROM BCDC_INPUT_BOM T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
            DELETE FROM BCDC_INPUT_DEMAND T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
            DELETE FROM BCDC_OUTPUT_DEMAND T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
            DELETE FROM BCDC_OUTPUT_SHORTAGE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
            DELETE FROM BCDC_EXECUTE_LOGS T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
            DELETE FROM BCDC_SHARE_AUTH T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR};
        END;
    </delete>

    <insert id="createReport1ByTable">
        INSERT INTO BCDC_INPUT_DEMAND
        (BATCH_ID,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{batchId, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
               #{list.${header}, jdbcType=VARCHAR}
            </foreach>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteReport1ByTable">
        DELETE FROM BCDC_INPUT_DEMAND WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <update id="updateReport1ByTable">
        UPDATE BCDC_INPUT_DEMAND
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>
        WHERE ROWID = #{rowid,jdbcType=VARCHAR}
          AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </update>

    <insert id="createStartByTable">
        INSERT INTO CB_BCDC_DEMAND_START_CLICK
        (BATCH_ID,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{batchId, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>
        </foreach>
    </insert>

    <delete id="deleteStartByTable">
        ALTER TABLE CB_BCDC_DEMAND_START_CLICK DELETE WHERE
        <foreach collection="deletes" open="(" close=")" separator=" OR " item="item">
            (MATERIAL = splitByChar('|', #{item, jdbcType=VARCHAR})[1] AND PLANT_CODE = splitByChar('|', #{item, jdbcType=VARCHAR})[2])
        </foreach>
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <update id="updateStartByTable">

        ALTER TABLE CB_BCDC_DEMAND_START_CLICK UPDATE
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>
        WHERE MATERIAL = splitByChar('|', #{rowid,jdbcType=VARCHAR})[1]
        AND PLANT_CODE = splitByChar('|', #{rowid,jdbcType=VARCHAR})[2]
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}

    </update>

    <insert id="createReport2ByTable">
        INSERT INTO CB_BCDC_INPUT_BOM_CLICK
        (BATCH_ID,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{batchId, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>
        </foreach>
    </insert>

    <delete id="deleteReport2ByTable">
        DELETE FROM CB_BCDC_INPUT_BOM_CLICK WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <update id="updateReport2ByTable">
        UPDATE CB_BCDC_INPUT_BOM_CLICK
        SET
        <foreach collection="updates" item="col" separator=",">
           ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>
        WHERE ROWID = #{rowid,jdbcType=VARCHAR}
          AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </update>

    <insert id="createReport11ByTable">
        INSERT INTO BCDC_FIX_RECOURSE
        (BATCH_ID,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{batchId, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteReport11ByTable">
        DELETE FROM BCDC_FIX_RECOURSE WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <update id="updateReport11ByTable">
        UPDATE BCDC_FIX_RECOURSE
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>
        WHERE ROWID = #{rowid,jdbcType=VARCHAR}
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </update>

    <insert id="createReport3ByTable">
        INSERT INTO BCDC_INPUT_RECOURSE
        (BATCH_ID,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{batchId, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteReport3ByTable">
        DELETE FROM BCDC_INPUT_RECOURSE WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <update id="updateReport3ByTable">
        UPDATE BCDC_INPUT_RECOURSE
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>
        WHERE ROWID = #{rowid,jdbcType=VARCHAR}
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </update>

    <insert id="createReport9ByTable">
        INSERT INTO BCDC_INPUT_RECOURSE
        (BATCH_ID,
        <foreach collection="headers" item="header" separator=",">
            <if test="header != 'BOM_COMPONENT'.toString()">
                ${header}
            </if>
            <if test="header == 'BOM_COMPONENT'.toString()">
                MATERIAL
            </if>
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{batchId, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteReport9ByTable">
        DELETE FROM BCDC_INPUT_RECOURSE WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <update id="updateReport9ByTable">
        UPDATE BCDC_INPUT_RECOURSE
        SET
        <foreach collection="updates" item="col" separator=",">
            <if test="col.key != 'BOM_COMPONENT'.toString()">
                ${col.key} = #{col.value,jdbcType=VARCHAR}
            </if>
            <if test="col.key == 'BOM_COMPONENT'.toString()">
                MATERIAL = #{col.value,jdbcType=VARCHAR}
            </if>
        </foreach>
        WHERE ROWID = #{rowid,jdbcType=VARCHAR}
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </update>


    <insert id="createReport7ByTable">
        INSERT INTO BCDC_INPUT_SUBSTITUTION
        (BATCH_ID,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{batchId, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteReport7ByTable">
        DELETE FROM BCDC_INPUT_SUBSTITUTION WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <update id="updateReport7ByTable">
        UPDATE BCDC_INPUT_SUBSTITUTION
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>
        WHERE ROWID = #{rowid,jdbcType=VARCHAR}
        AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </update>

    <select id="queryBatchParams" resultType="java.lang.String">
        SELECT PARAMS FROM CB_BCDC_LOG T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </select>

    <delete id="deleteStartData">
        DELETE FROM CB_BCDC_DEMAND_START_CLICK T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <insert id="insertStartData" parameterType="com.scp.simulation.bean.DemandInput">
        INSERT /*+ NOLOGGING */ INTO CB_BCDC_DEMAND_START_CLICK
        (BATCH_ID, MATERIAL, PLANT_CODE)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{batchId, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.plant_code, jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <delete id="deleteReport1Data">
        DELETE FROM BCDC_INPUT_DEMAND T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport1Data" parameterType="com.scp.simulation.bean.DemandInput">
        INSERT /*+ NOLOGGING */ INTO BCDC_INPUT_DEMAND
        (BATCH_ID, PRIMARY_KEY, MATERIAL, QTY, PRIORITY)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{batchId, jdbcType=VARCHAR},
            #{item.primaryKey, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.qty, jdbcType=DOUBLE},
            #{item.priority, jdbcType=INTEGER}
             from dual
        </foreach>
    </insert>

    <delete id="deleteReport2Data">
        DELETE FROM CB_BCDC_INPUT_BOM_CLICK T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport2Data" parameterType="com.scp.simulation.bean.BomInput">
        INSERT /*+ NOLOGGING */ INTO CB_BCDC_INPUT_BOM_CLICK
        (BATCH_ID, MATERIAL, BOM_COMPONENT, "USAGE")
        <foreach collection="list" separator=" union all" item="item">
            select
            #{batchId, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.bomComponent, jdbcType=VARCHAR},
            #{item.usage, jdbcType=DOUBLE}
        </foreach>
    </insert>

    <delete id="deleteReport11Data">
        DELETE FROM BCDC_FIX_RECOURSE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport11Data" parameterType="com.scp.simulation.bean.BomInput">
        INSERT /*+ NOLOGGING */ INTO BCDC_INPUT_BOM
        (BATCH_ID, MATERIAL, BOM_COMPONENT, "USAGE")
        <foreach collection="list" separator=" union all" item="item">
            select
            #{batchId, jdbcType=VARCHAR},
            #{item.fixRecourse, jdbcType=VARCHAR}

            from dual
        </foreach>
    </insert>

    <delete id="deleteReport3Data">
        DELETE FROM BCDC_INPUT_RECOURSE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport3Data" parameterType="com.scp.simulation.bean.RecourseInput">
        INSERT /*+ NOLOGGING */ INTO BCDC_INPUT_RECOURSE
        (BATCH_ID, MATERIAL,STEP0 ,STEP1 ,STEP2 ,STEP3 ,STEP4 ,STEP5 ,STEP6 ,STEP7 ,STEP8 ,STEP9 ,STEP10,
        STEP11,STEP12,STEP13,STEP14,STEP15,STEP16,STEP17,STEP18,STEP19,STEP20,
        STEP21,STEP22,STEP23,STEP24,STEP25,STEP26,STEP27,STEP28,STEP29,STEP30,
        STEP31,STEP32,STEP33,STEP34,STEP35)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{batchId, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.step0, jdbcType=DOUBLE},
            #{item.step1, jdbcType=DOUBLE},
            #{item.step2, jdbcType=DOUBLE},
            #{item.step3, jdbcType=DOUBLE},
            #{item.step4, jdbcType=DOUBLE},
            #{item.step5, jdbcType=DOUBLE},
            #{item.step6, jdbcType=DOUBLE},
            #{item.step7, jdbcType=DOUBLE},
            #{item.step8, jdbcType=DOUBLE},
            #{item.step9, jdbcType=DOUBLE},
            #{item.step10, jdbcType=DOUBLE},
            #{item.step11, jdbcType=DOUBLE},
            #{item.step12, jdbcType=DOUBLE},
            #{item.step13, jdbcType=DOUBLE},
            #{item.step14, jdbcType=DOUBLE},
            #{item.step15, jdbcType=DOUBLE},
            #{item.step16, jdbcType=DOUBLE},
            #{item.step17, jdbcType=DOUBLE},
            #{item.step18, jdbcType=DOUBLE},
            #{item.step19, jdbcType=DOUBLE},
            #{item.step20, jdbcType=DOUBLE},
            #{item.step21, jdbcType=DOUBLE},
            #{item.step22, jdbcType=DOUBLE},
            #{item.step23, jdbcType=DOUBLE},
            #{item.step24, jdbcType=DOUBLE},
            #{item.step25, jdbcType=DOUBLE},
            #{item.step26, jdbcType=DOUBLE},
            #{item.step27, jdbcType=DOUBLE},
            #{item.step28, jdbcType=DOUBLE},
            #{item.step29, jdbcType=DOUBLE},
            #{item.step30, jdbcType=DOUBLE},
            #{item.step31, jdbcType=DOUBLE},
            #{item.step32, jdbcType=DOUBLE},
            #{item.step33, jdbcType=DOUBLE},
            #{item.step34, jdbcType=DOUBLE},
            #{item.step35, jdbcType=DOUBLE}
             from dual
        </foreach>
    </insert>

    <delete id="deleteReport9Data">
        DELETE FROM BCDC_INPUT_RECOURSE T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport9Data" parameterType="com.scp.simulation.bean.RecourseInput">
        INSERT /*+ NOLOGGING */ INTO BCDC_INPUT_RECOURSE
        (BATCH_ID, MATERIAL,STEP0 )
        <foreach collection="list" separator=" union all" item="item">
            select
            #{batchId, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.step0, jdbcType=DOUBLE}
            from dual
        </foreach>
    </insert>

    <delete id="deleteReport7Data">
        DELETE FROM BCDC_INPUT_SUBSTITUTION T WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport7Data" parameterType="com.scp.simulation.bean.SubstitutionInput">
        INSERT /*+ NOLOGGING */ INTO BCDC_INPUT_SUBSTITUTION
        (BATCH_ID, MATERIAL_IN_BOM, FOLLOW_UP_MATERIAL)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{batchId, jdbcType=VARCHAR},
            #{item.materialInBom, jdbcType=VARCHAR},
            #{item.followUpMaterial, jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <select id="queryExecutingCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM CB_BCDC_LOG T WHERE T.START_TIME IS NOT NULL AND T.END_TIME IS NULL AND T.START_TIME > SYSDATE - 0.5
    </select>

    <sql id="queryTaskQueueSQL">
        SELECT WORK_MODE,
               T2.USER_NAME || '(' || USERID || ')' AS USERNAME,
               BATCH_ID,
               TO_CHAR(START_TIME, 'YYYY/MM/DD HH24:MI:SS') START_TIME,
               ROUND((END_TIME - START_TIME) * 1440, 1) EXEC_TIME_IN_MIN,
               NAME,
               TO_CHAR(CREATE_DATE, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE,
               TO_CHAR(END_TIME, 'YYYY/MM/DD HH24:MI:SS') END_TIME,
               DECODE(T.END_TIME, NULL, CASE WHEN T.START_TIME > SYSDATE - 0.5 THEN 'Interrupted' ELSE 'Processing' END, 'Finished') STATUS
          FROM CB_BCDC_LOG T INNER JOIN SY_USER_MASTER_DATA T2 ON T.USERID = T2.SESA_CODE
         WHERE T.START_TIME IS NOT NULL AND BATCH_ID =  #{batchId, jdbcType=VARCHAR}
         ORDER BY DECODE(T.END_TIME, NULL, SYSDATE, T.END_TIME) DESC, START_TIME DESC
    </sql>

    <select id="queryTaskQueueCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryTaskQueueSQL"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryTaskQueue" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryTaskQueueSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport4SQL">
        WITH BCDC_BIP AS (
            SELECT
                OUTD.BATCH_ID,
                OUTD.PRIMARY_KEY,
                IND.MATERIAL,
                IND.PLANT_CODE,
                IND.PRIORITY,
                IND.QTY,
                OUTPUT_QTY * MMV.UNIT_COST AS ORDER_VALUE,
                OUTD.ADD_STEPS,
                OUTPUT_QTY AS BCDC_QTY
            FROM SCPA.BCDC_OUTPUT_DEMAND OUTD
            LEFT JOIN SCPA.BCDC_INPUT_DEMAND IND
            ON IND.BATCH_ID = OUTD.BATCH_ID AND IND.PRIMARY_KEY = OUTD.PRIMARY_KEY
            LEFT JOIN SCPA.MATERIAL_MASTER_V MMV ON MMV.MATERIAL =IND.MATERIAL AND MMV.PLANT_CODE =IND.PLANT_CODE),
        PIVOT AS (
            SELECT *
             FROM BCDC_BIP
            PIVOT (
                SUM(ORDER_VALUE) AS ORDER_VALUE,
                SUM(BCDC_QTY) BCDC_QTY
                FOR ADD_STEPS IN (
                <foreach collection="steps" separator="," item="item" index="index">
                    '${index}'
                </foreach>
                )
            )
            WHERE BATCH_ID = #{batchId, jdbcType=VARCHAR}
        )
        SELECT
               PRIMARY_KEY,
               PLANT_CODE,
               MATERIAL,
               PRIORITY,
               QTY
               <foreach collection="steps" separator="," item="item" index="index" open=",">
                "'${index}'_BCDC_QTY" AS "STEP_${item}_QTY"
               </foreach>
              <foreach collection="steps" separator="+" item="item" index="index" open=",">
                      "'${index}'_ORDER_VALUE"
              </foreach>
              AS ORDER_VALUE
        FROM PIVOT T
    </sql>

    <select id="queryReport4Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryReport4SQL"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport4SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport5SQL">
        SELECT S.PRIMARY_KEY, S.MATERIAL, S.PRIORITY, S.BOM_COMPONENT, S.SHORTAGE_QTY,MMV.MATERIAL_OWNER_NAME,PO.EARLIEST_CONFIRMED_DELIVERY_DATE,MMV.VENDOR_FULL_NAME
        FROM SCPA.BCDC_OUTPUT_SHORTAGE S
        LEFT JOIN SCPA.BCDC_INPUT_DEMAND D ON D.BATCH_ID =S.BATCH_ID AND D.PRIMARY_KEY =S.PRIMARY_KEY
        LEFT JOIN SCPA.MATERIAL_MASTER_V MMV ON MMV.MATERIAL = S.BOM_COMPONENT AND MMV.PLANT_CODE = D.PLANT_CODE
        LEFT JOIN (SELECT MATERIAL,PLANT_CODE,MIN(CONFIRMED_DELIVERY_DATE) AS EARLIEST_CONFIRMED_DELIVERY_DATE FROM SCPA.OPEN_PO_STRUCTURE_V GROUP BY MATERIAL, PLANT_CODE ) PO ON PO.MATERIAL = S.BOM_COMPONENT AND PO.PLANT_CODE = D.PLANT_CODE
        where S.BATCH_ID = #{batchId, jdbcType=VARCHAR} AND SHORTAGE_QTY &lt; 0
    </sql>

    <select id="queryReport5Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryReport5SQL"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryReport5" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport5SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport10SQL">
        SELECT MATERIAL,MAX_QTY FROM SCPA.BCDC_OUTPUT_MAX
        where BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport10Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport10SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport10" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport10SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport8SQL">
        SELECT S.MATERIAL, S.REMAIN_QTY,MMV.UNIT_COST * REMAIN_QTY AS VALUE
        FROM SCPA.BCDC_OUTPUT_REMAIN_RECOURSE S
                LEFT JOIN CB_BCDC_LOG D
                ON D.BATCH_ID = S.BATCH_ID
                LEFT JOIN SCPA.MATERIAL_MASTER_V MMV ON MMV.MATERIAL = S.MATERIAL AND MMV.PLANT_CODE = JSON_VALUE(PARAMS, '$.plant')
        where S.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport8Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport8SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport8" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport8SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport6SQL">
        SELECT *
          FROM scpa.BCD_OUTPUT_SHORTAGE_V
         WHERE SHORTAGE &lt; 0
           AND BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport6Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryReport6SQL"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryReport6" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport6SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport7SQL">
        SELECT ROWIDTOCHAR(ROWID) ROW_ID,MATERIAL_IN_BOM, FOLLOW_UP_MATERIAL
        FROM SCPA.BCDC_INPUT_SUBSTITUTION
        WHERE BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport7Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport7SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport7" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport7SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryAuthDetails" resultType="java.lang.String">
        select auth_details from SY_MENU_AUTH where lower(user_id) = lower(#{userid, jdbcType=VARCHAR}) and menu_code = #{menuCode, jdbcType=VARCHAR}
    </select>

    <select id="queryShareAuth" resultType="java.lang.String">
        SELECT DISTINCT BATCH_ID from BCDC_SHARE_AUTH where USER_ID = #{userid, jdbcType=VARCHAR}
    </select>

    <select id="queryAvailablePlants" resultType="java.lang.String">
        SELECT T.PLANT_CODE FROM MR3_PLANT_MASTER_DATA T WHERE T.PLANT_TYPE = 'FAC' ORDER BY T.PLANT_CODE
    </select>

    <select id="queryBatchStepInfo" resultType="java.util.Map">
        SELECT MAX(ADD_STEPS)+1 AS STEP
        FROM BCDC_OUTPUT_DEMAND T
        WHERE T.BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </select>

    <select id="queryBatchStepName" resultType="java.lang.String">
        SELECT DISTINCT (ADD_STEPS) AS STEP
            FROM BCDC_OUTPUT_DEMAND T
        WHERE BATCH_ID = #{batchId, jdbcType=VARCHAR}
        ORDER BY STEP
    </select>

    <update id="updateSimulateStep">
         UPDATE CB_BCDC_LOG
            SET STEP = #{step, jdbcType=INTEGER},
                STEP_MODE = #{stepMode, jdbcType=VARCHAR}
          WHERE BATCH_ID = #{batchId, jdbcType=VARCHAR}
     </update>

    <update id="updatePlant">
        UPDATE BCDC_INPUT_DEMAND
        SET PLANT_CODE = #{plant, jdbcType=VARCHAR}
        WHERE BATCH_ID = #{batchId, jdbcType=VARCHAR}
    </update>

    <resultMap id="compareReportL1Map" type="com.scp.simulation.bean.BestCanDoCompare1">
        <result property="HORIZONTAL" column="HORIZONTAL"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="compareReport1legend" resultType="java.lang.String" parameterType="java.util.Map">
        SELECT DISTINCT LEGEND
        FROM BCD_COMPARE_OUTPUT_V
    </select>

    <select id="compareReport1" parameterType="java.util.Map" resultMap="compareReportL1Map">
        <choose>
            <when test="compareTaskR == null or compareTaskR == ''.toString()">
                SELECT LEGEND AS NAME, ${compare1Value} AS VALUE, 'L_' || ${compare1XAxisType} AS HORIZONTAL
                FROM BCD_COMPARE_OUTPUT_V
                WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                <if test="selectedxAxis != null and selectedxAxis.size() > 0">
                    AND 'L_' || ${compare1XAxisType} IN
                    (<foreach collection="selectedxAxis" separator="," item="item">
                    '${item}'
                </foreach>)
                </if>
                GROUP BY LEGEND,'L_' || ${compare1XAxisType}
                ORDER BY 'L_' || ${compare1XAxisType}
            </when>
            <otherwise>
                WITH COMPARE1 AS (SELECT LEGEND AS NAME, ${compare1Value} AS VALUE, 'L_' || ${compare1XAxisType} AS HORIZONTAL
                    FROM BCD_COMPARE_OUTPUT_V
                    WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                    <if test="selectedxAxis != null and selectedxAxis.size() > 0">
                        AND 'L_' || ${compare1XAxisType} IN
                        (<foreach collection="selectedxAxis" separator="," item="item">
                        '${item}'
                    </foreach>)
                    </if>
                    GROUP BY LEGEND,'L_' || ${compare1XAxisType}
                    ORDER BY 'L_' || ${compare1XAxisType}),
                COMPARE2 AS (SELECT LEGEND AS NAME, ${compare1Value} AS VALUE, 'R_' || ${compare1XAxisType} AS HORIZONTAL
                    FROM BCD_COMPARE_OUTPUT_V
                    WHERE BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                    <if test="selectedxAxis != null and selectedxAxis.size() > 0">
                        AND 'R_' || ${compare1XAxisType} IN
                        (<foreach collection="selectedxAxis" separator="," item="item">
                        '${item}'
                    </foreach>)
                    </if>
                    GROUP BY LEGEND,'R_' || ${compare1XAxisType}
                    ORDER BY 'R_' || ${compare1XAxisType})
                SELECT * FROM COMPARE1
                    UNION
                SELECT * FROM COMPARE2
            </otherwise>
        </choose>
    </select>

    <resultMap id="compareReport2ResultMap" type="com.scp.simulation.bean.BestCanDoCompare2Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="value" column="value"/>
    </resultMap>

    <select id="compareReport2" resultMap="compareReport2ResultMap">
        SELECT BATCH_ID   AS CATEGORY1,
        NVL(${compare2XAxisType}, 'Others') AS CATEGORY2,
        ${compare2Value} AS VALUE
        FROM BCD_COMPARE_SHORTAGE_SUMMARY_V
        WHERE BATCH_ID IN (
            #{compareTaskL, jdbcType=VARCHAR},
            #{compareTaskR, jdbcType=VARCHAR})
        GROUP BY BATCH_ID,${compare2XAxisType}
    </select>

    <select id="compareReport3legend" resultType="java.lang.String" parameterType="java.util.Map">
        <choose>
            <when test="compareTaskR == null or compareTaskR == ''.toString()">
                SELECT MATERIAL AS LEGEND
                FROM BCD_COMPARE_SHORTAGE_DETAIL_V
                WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                <if test="compare3SelectedxAxis != null and compare3SelectedxAxis.size() > 0">
                    AND 'L_' || ${compare3XAxisType} IN
                    (<foreach collection="compare3SelectedxAxis" separator="," item="item">
                    '${item}'
                </foreach>)
                </if>
            </when>
            <otherwise>
                WITH COMPARE1 AS (SELECT MATERIAL AS LEGEND
                    FROM BCD_COMPARE_SHORTAGE_DETAIL_V
                    WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                <if test="compare3SelectedxAxis != null and compare3SelectedxAxis.size() > 0">
                    AND 'L_' || ${compare3XAxisType} IN
                    (<foreach collection="compare3SelectedxAxis" separator="," item="item">
                    '${item}'
                </foreach>)
                </if>),
                COMPARE2 AS (SELECT MATERIAL AS LEGEND
                FROM BCD_COMPARE_SHORTAGE_DETAIL_V
                WHERE BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                <if test="compare3SelectedxAxis != null and compare3SelectedxAxis.size() > 0">
                    AND 'R_' || ${compare3XAxisType} IN
                    (<foreach collection="compare3SelectedxAxis" separator="," item="item">
                    '${item}'
                </foreach>)
                </if>)
                SELECT * FROM COMPARE1
                UNION
                SELECT * FROM COMPARE2
            </otherwise>
        </choose>
    </select>

    <select id="compareReport3" parameterType="java.util.Map" resultMap="compareReportL1Map">
        <choose>
            <when test="compareTaskR == null or compareTaskR == ''.toString()">
                SELECT MATERIAL AS NAME,  'L_' || ${compare3XAxisType} AS HORIZONTAL, ${compare3Value} AS VALUE
                FROM BCD_COMPARE_SHORTAGE_DETAIL_V
                WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                <if test="compare3SelectedxAxis != null and compare3SelectedxAxis.size() > 0">
                    AND 'L_' || ${compare3XAxisType} IN
                    (<foreach collection="compare3SelectedxAxis" separator="," item="item">
                    '${item}'
                </foreach>)
                </if>
                GROUP BY MATERIAL,'L_' || ${compare3XAxisType}
                ORDER BY ${compare3Value}
            </when>
            <otherwise>
                WITH COMPARE1 AS (SELECT MATERIAL AS NAME, ${compare3Value} AS VALUE, 'L_' || ${compare3XAxisType} AS HORIZONTAL
                    FROM BCD_COMPARE_SHORTAGE_DETAIL_V
                    WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                    <if test="compare3SelectedxAxis != null and compare3SelectedxAxis.size() > 0">
                        AND 'L_' || ${compare3XAxisType} IN
                        (<foreach collection="compare3SelectedxAxis" separator="," item="item">
                        '${item}'
                    </foreach>)
                    </if>
                    GROUP BY MATERIAL,'L_' || ${compare3XAxisType}
                    ORDER BY ${compare3XAxisType}),
                COMPARE2 AS (SELECT MATERIAL AS NAME, ${compare3Value} AS VALUE, 'R_' || ${compare3XAxisType} AS HORIZONTAL
                    FROM BCD_COMPARE_SHORTAGE_DETAIL_V
                    WHERE BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                    <if test="compare3SelectedxAxis != null and compare3SelectedxAxis.size() > 0">
                        AND 'R_' || ${compare3XAxisType} IN
                        (<foreach collection="compare3SelectedxAxis" separator="," item="item">
                        '${item}'
                    </foreach>)
                    </if>
                    GROUP BY MATERIAL,'R_' || ${compare3XAxisType}
                    ORDER BY ${compare3XAxisType})
                SELECT * FROM COMPARE1
                UNION
                SELECT * FROM COMPARE2
            </otherwise>
        </choose>
    </select>

    <select id="compareReport4legend" resultType="java.lang.String" parameterType="java.util.Map">
        SELECT 'SOH_LEFT' AS LEGEND
        FROM DUAL
        UNION ALL
        SELECT 'USAGE' AS LEGEND
        FROM DUAL
        UNION ALL
        SELECT 'WIP_PROD' AS LEGEND
        FROM DUAL
    </select>

    <select id="compareReport4Left" parameterType="java.util.Map" resultMap="compareReportL1Map">
        with A as(
            SELECT 'SOH_LEFT' AS NAME, T.SOH_LEFT AS VALUE, T1.STEP_MODE || '+' || T.ADD_STEPS AS HORIZONTAL, T.UNIT_COST
        FROM BCD_OUTPUT_SOH_EVOLUTION_V T
            LEFT JOIN CB_BCDC_LOG T1 ON T.BATCH_ID = T1.BATCH_ID
        WHERE T.BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
        UNION ALL
        SELECT 'USAGE' AS NAME, T.USAGE AS VALUE, T1.STEP_MODE || '+' || T.ADD_STEPS AS HORIZONTAL, T.UNIT_COST
        FROM BCD_OUTPUT_SOH_EVOLUTION_V T
            LEFT JOIN CB_BCDC_LOG T1 ON T.BATCH_ID = T1.BATCH_ID
        WHERE T.BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
        UNION ALL
        SELECT 'WIP_PROD' AS NAME, T.WIP_PROD AS VALUE, T1.STEP_MODE || '+' || T.ADD_STEPS AS HORIZONTAL, T.UNIT_COST
        FROM BCD_OUTPUT_SOH_EVOLUTION_V T
            LEFT JOIN CB_BCDC_LOG T1 ON T.BATCH_ID = T1.BATCH_ID
        WHERE T.BATCH_ID = #{compareTaskL, jdbcType=VARCHAR})
        SELECT NAME,HORIZONTAL,${compare4Value} AS VALUE
        FROM A
        GROUP BY NAME,HORIZONTAL
    </select>

    <select id="compareReport4Right" parameterType="java.util.Map" resultMap="compareReportL1Map">
        with A as(
            SELECT 'SOH_LEFT' AS NAME, T.SOH_LEFT AS VALUE, T1.STEP_MODE || '+' || T.ADD_STEPS AS HORIZONTAL, T.UNIT_COST
        FROM BCD_OUTPUT_SOH_EVOLUTION_V T
            LEFT JOIN CB_BCDC_LOG T1 ON T.BATCH_ID = T1.BATCH_ID
        WHERE T.BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
        UNION ALL
        SELECT 'USAGE' AS NAME, T.USAGE AS VALUE, T1.STEP_MODE || '+' || T.ADD_STEPS AS HORIZONTAL, T.UNIT_COST
        FROM BCD_OUTPUT_SOH_EVOLUTION_V T
            LEFT JOIN CB_BCDC_LOG T1 ON T.BATCH_ID = T1.BATCH_ID
        WHERE T.BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
        UNION ALL
        SELECT 'WIP_PROD' AS NAME, T.WIP_PROD AS VALUE, T1.STEP_MODE || '+' || T.ADD_STEPS AS HORIZONTAL, T.UNIT_COST
        FROM BCD_OUTPUT_SOH_EVOLUTION_V T
            LEFT JOIN CB_BCDC_LOG T1 ON T.BATCH_ID = T1.BATCH_ID
        WHERE T.BATCH_ID = #{compareTaskR, jdbcType=VARCHAR})
        SELECT NAME,HORIZONTAL,${compare4Value} AS VALUE
        FROM A
        GROUP BY NAME,HORIZONTAL
    </select>

    <sql id="compareReport1Details">
        <choose>
            <when test="isLeftTask == true">
                SELECT * FROM BCD_COMPARE_OUTPUT_V
                    WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                    AND LEGEND = #{compare1SelectedSeriesName, jdbcType=VARCHAR}
                    AND 'L_' || ${compare1XAxisType} = #{compare1SelectedName, jdbcType=VARCHAR}
            </when>
            <when test="isLeftTask == false">
                SELECT * FROM BCD_COMPARE_OUTPUT_V
                WHERE BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                AND LEGEND = #{compare1SelectedSeriesName, jdbcType=VARCHAR}
                AND 'R_' || ${compare1XAxisType} = #{compare1SelectedName, jdbcType=VARCHAR}
            </when>
        </choose>
    </sql>

    <select id="compareReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="compareReport1Details"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="compareReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="compareReport1Details"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="compareReport2Details">
        <choose>
            <when test="isLeftTask == true">
                SELECT *
                FROM BCD_COMPARE_SHORTAGE_SUMMARY_V
                WHERE BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                <choose>
                    <when test="compare2SelectedSeriesName == 'Others'">
                        AND ${compare2XAxisType} is null
                    </when>
                    <otherwise>
                        AND ${compare2XAxisType} = #{compare2SelectedSeriesName, jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </when>
            <when test="isLeftTask == false">
                SELECT *
                FROM BCD_COMPARE_SHORTAGE_SUMMARY_V
                WHERE BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                <choose>
                    <when test="compare2SelectedSeriesName == 'Others'">
                        AND ${compare2XAxisType} is null
                    </when>
                    <otherwise>
                        AND ${compare2XAxisType} = #{compare2SelectedSeriesName, jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </when>
        </choose>
    </sql>

    <select id="compareReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="compareReport2Details"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="compareReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="compareReport2Details"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="compareReport3Details">
        <choose>
            <when test="isLeftTask == true">
                select *
                from BCD_COMPARE_SHORTAGE_DETAIL_V
                where BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                <if test="compare3SelectedName != null and compare3SelectedName != ''.toString()">
                    AND MATERIAL = #{compare3SelectedSeriesName, jdbcType=VARCHAR}
                    AND 'L_' || ${compare3XAxisType} = #{compare3SelectedName, jdbcType=VARCHAR}
                </if>
            </when>
            <when test="isLeftTask == false">
                select *
                from BCD_COMPARE_SHORTAGE_DETAIL_V
                where BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                <if test="compare3SelectedName != null and compare3SelectedName != ''.toString()">
                    AND MATERIAL = #{compare3SelectedSeriesName, jdbcType=VARCHAR}
                    AND 'R_' || ${compare3XAxisType} = #{compare3SelectedName, jdbcType=VARCHAR}
                </if>
            </when>
        </choose>
    </sql>

    <select id="compareReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="compareReport3Details"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="compareReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="compareReport3Details"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="compareReport4Details">
        SELECT *
        FROM BCD_OUTPUT_SOH_EVOLUTION_V T
        LEFT JOIN CB_BCDC_LOG T1 ON T.BATCH_ID = T1.BATCH_ID
        WHERE T.BATCH_ID = #{compareTask, jdbcType=VARCHAR}
        AND T1.STEP_MODE || '+' || T.ADD_STEPS = #{compare4SelectedName, jdbcType=VARCHAR}
    </sql>

    <select id="compareReport4DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="compareReport4Details"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="compareReport4Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="compareReport4Details"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="compareReportOverview" resultType="java.util.Map">
        WITH A AS (select LEGEND, ${compareOverviewValue} AS VALUE
        from scpa.BCD_COMPARE_OUTPUT_V
        where BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
        GROUP BY LEGEND),
            B AS (select LEGEND, ${compareOverviewValue} AS VALUE
        from scpa.BCD_COMPARE_OUTPUT_V
        where BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
        group by LEGEND),
            LEFT_BCD as (SELECT VALUE AS LEFT_BCD FROM A WHERE LEGEND = 'BCD'),
            LEFT_DEMAND as (SELECT VALUE AS LEFT_DEMAND FROM A WHERE LEGEND = 'DEMAND'),
            LEFT_SEMI_FINISH_GOODS as (SELECT VALUE AS LEFT_SEMI_FINISH_GOODS FROM A WHERE LEGEND = 'SEMI-FINISH GOODS'),
            RIGHT_BCD as (SELECT VALUE AS RIGHT_BCD FROM B WHERE LEGEND = 'BCD'),
            RIGHT_DEMAND as (SELECT VALUE AS RIGHT_DEMAND FROM B WHERE LEGEND = 'DEMAND'),
            RIGHT_SEMI_FINISH_GOODS as (SELECT VALUE AS RIGHT_SEMI_FINISH_GOODS FROM B WHERE LEGEND = 'SEMI-FINISH GOODS'),
            T AS (SELECT SYSDATE AS AUXILIARY_COLUMN FROM DUAL)
        SELECT NVL(LEFT_BCD, 0)                AS LEFT_BCD,
               NVL(LEFT_DEMAND, 0)             AS LEFT_DEMAND,
               NVL(LEFT_SEMI_FINISH_GOODS, 0)  AS LEFT_SEMI_FINISH_GOODS,
               NVL(RIGHT_BCD, 0)               AS RIGHT_BCD,
               NVL(RIGHT_DEMAND, 0)            AS RIGHT_DEMAND,
               NVL(RIGHT_SEMI_FINISH_GOODS, 0) AS RIGHT_SEMI_FINISH_GOODS,
               T.AUXILIARY_COLUMN
        FROM T
                 LEFT JOIN LEFT_BCD ON 1 = 1
                 LEFT JOIN RIGHT_SEMI_FINISH_GOODS ON 1 = 1
                 LEFT JOIN LEFT_DEMAND ON 1 = 1
                 LEFT JOIN LEFT_SEMI_FINISH_GOODS ON 1 = 1
                 LEFT JOIN RIGHT_BCD ON 1 = 1
                 LEFT JOIN RIGHT_DEMAND ON 1 = 1
                 LEFT JOIN DUAL ON 1=1
    </select>

    <sql id="compareOverviewDetailsSQL">
        SELECT * FROM BCD_COMPARE_OUTPUT_V
        <where>
            <choose>
                <when test="compareOverviewDetailsType == 'LEFT_BEST_CAN_DO'.toString()">
                    BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                    AND
                    LEGEND = 'BCD'
                </when>
                <when test="compareOverviewDetailsType == 'LEFT_DEMAND'.toString()">
                    BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                    AND
                    LEGEND = 'DEMAND'
                </when>
                <when test="compareOverviewDetailsType == 'LEFT_SEMI_FINISH_GOODS'.toString()">
                    BATCH_ID = #{compareTaskL, jdbcType=VARCHAR}
                    AND
                    LEGEND = 'SEMI-FINISH GOODS'
                </when>
                <when test="compareOverviewDetailsType == 'RIGHT_BCD'.toString()">
                    BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                    AND
                    LEGEND = 'BCD'
                </when>
                <when test="compareOverviewDetailsType == 'RIGHT_DEMAND'.toString()">
                    BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                    AND
                    LEGEND = 'DEMAND'
                </when>
                <when test="compareOverviewDetailsType == 'RIGHT_SEMI_FINISH_GOODS'.toString()">
                    BATCH_ID = #{compareTaskR, jdbcType=VARCHAR}
                    AND
                    LEGEND = 'SEMI-FINISH GOODS'
                </when>
            </choose>
        </where>
    </sql>

    <select id="compareOverviewDetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="compareOverviewDetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="compareOverviewDetails" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="compareOverviewDetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <insert id="shareCondition">
        DECLARE
        PARAMS_COLB CLOB := #{remarks, jdbcType=CLOB};
        BEGIN
        INSERT INTO BCDC_SHARE_AUTH
        (USER_ID, BATCH_ID, CREATE_BY$, CREATE_DATE$)
        <foreach collection="users" separator=" union all" item="item" index="index">
            SELECT #{item, jdbcType=VARCHAR}, #{batch_id, jdbcType=VARCHAR}, #{userid, jdbcType=VARCHAR}, SYSDATE
            FROM DUAL
        </foreach>;
        END;
    </insert>

</mapper>
