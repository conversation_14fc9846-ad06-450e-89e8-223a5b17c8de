package com.dia.mpt.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.dia.mpt.dao.IMPTWorkspaceDao;
import com.dia.mpt.service.IMPTWorkspaceService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.mail.MailBean;
import com.starter.context.mail.MailFeignClient;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class MPTWorkspaceServiceImpl implements IMPTWorkspaceService {

    public final static String PARENT_CODE = "menuC03";

    @Resource
    private IMPTWorkspaceDao mptWorkspaceDao;

    @Resource
    private Response response;

    @Resource
    private MailFeignClient mailFeignClient;

    private final String CODE_WAITING_FOR_APPROVAL = "Waiting for Approval";

    private final String CODE_WAITING_FOR_ACCEPTANCE = "Waiting for Acceptance";

    private final String CODE_TAKING_EFFECT = "Taking Effect";

    private final String CODE_REJECTED = "Reject";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initNewTickets(Map<String, Object> parameterMap, Session session) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, String>> collaboratorsOpts = mptWorkspaceDao.queryCollaboratorsOpts(session.getUserid());
        resultMap.put("plantApprovalOpts", Utils.parseCascader(mptWorkspaceDao.queryPlantApprovalOptsOpts(session.getUserid()), false));
        resultMap.put("centralApprovalOpts", Utils.parseCascader(mptWorkspaceDao.queryCentralApprovalOptsOpts(), false));
        resultMap.put("collaboratorsOpts", Utils.parseCascader(collaboratorsOpts, false));
        Map<String, String> manager = mptWorkspaceDao.queryCollaboratorsDefault(session.getUserid());
        List<String> collaborators = new ArrayList<>();
        if (manager != null && manager.get("ENTITY_NAME") != null) {
            collaborators.add(manager.get("ENTITY_NAME"));
            collaborators.add(manager.get("SESA_CODE"));
        }
        resultMap.put("manager", collaborators);
        resultMap.put("categoryOpts", mptWorkspaceDao.queryCategoryOpts());
        resultMap.put("ownerOpts", mptWorkspaceDao.queryOwnerOpts());
        return response.setBody(resultMap);
    }

    @Override
    public Map<String, Object> initViewTickets(Map<String, Object> parameterMap, Session session) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("isAdmin", mptWorkspaceDao.queryAdminCnt(PARENT_CODE, session.getUserid()) > 0);
        return resultMap;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1h")
    public Response querySubjectOpts(Map<String, Object> parameterMap) {
        return response.setBody(mptWorkspaceDao.querySubjectOpts(parameterMap));
    }

    // region save new ticket
    @Override
    @SuppressWarnings("unchecked")
    public void saveNewTicket(Map<String, Object> parameterMap, Session session) {
        String userid = session.getUserid();
        String username = session.getUsername();
        String id = Utils.randomStr(16);
        String title = (String) parameterMap.get("title");

        parameterMap.put("subjects", JSON.toJSONString(parameterMap.get("subject")));
        parameterMap.put("requestBy", userid);
        parameterMap.put("id", id);

        String action = (String) parameterMap.get("action");
        List<String> users = new ArrayList<>();
        String content = null;
        switch (action) {
            // users 为前台输入
            // 如果是Feedback, ACTION_OWNER无需维护
            // 问题状态为 CODE_TAKING_EFFECT
            // users字段为合作人
            case "1. Feedback" -> {
                List<List<String>> collaborators = (List<List<String>>) parameterMap.get("collaborators");

                for (List<String> temp : collaborators) {
                    users.add(temp.get(1));
                }

                parameterMap.put("status", CODE_TAKING_EFFECT);
                parameterMap.put("actionOwner", null);
                parameterMap.put("assignedBy", userid);
                parameterMap.put("collaborators", JSON.toJSONString(users));

                content = username + " mentioned you in ticket【" + title + "】";
            }
            // 如果是Exclusion, ACTION_OWNER无需维护
            // 问题状态为 CODE_WAITING_FOR_APPROVAL
            // users字段为approver
            case "2. Exclusion" -> {
                List<String> collaborators = (List<String>) parameterMap.remove("collaborators");

                parameterMap.put("status", CODE_WAITING_FOR_APPROVAL);
                parameterMap.put("actionOwner", null);
                parameterMap.put("assignedBy", userid);
                parameterMap.put("approver", collaborators.get(1));

                users.add(collaborators.get(1));
                content = username + " has submitted a ticket【" + title + "】 for your approval";
            }
            // 如果是Forward, 则ACTION_OWNER = users
            // ASSIGNED_BY = REQUEST_BY
            // 问题状态为Waiting for Acceptance
            // users字段为ACTION_OWNER
            case "3. Forward" -> {
                List<String> collaborators = (List<String>) parameterMap.remove("collaborators");

                parameterMap.put("status", "Waiting for Acceptance");
                parameterMap.put("actionOwner", collaborators.get(1));
                parameterMap.put("assignedBy", userid);

                users.add(collaborators.get(1));
                content = username + " forwarded a ticket【" + title + "】 to you";
            }
            case "4. Escalation" -> {
                List<String> collaborators = (List<String>) parameterMap.remove("collaborators");

                parameterMap.put("status", CODE_TAKING_EFFECT); // 升级不需要接收, 直接升级, 和自动升级的流程保持匹配
                parameterMap.put("actionOwner", collaborators.get(1));
                parameterMap.put("assignedBy", userid);

                users.add(collaborators.get(1));
                content = username + " need your help with ticket【" + title + "】";
            }
        }

        mptWorkspaceDao.saveTicket(parameterMap);
        this.saveNotice(id, content, userid, users);
        //插入一条Log至MY_PLANNING_TODAY_TIKECTS_LOGS
        this.saveLog(id, userid, "Submitted a ticket");
        // 给自己发一个通知, 告诉Ticket创建成功
        this.saveNotice(id, "Ticket 【" + title + "】 is created", userid, userid);
        // 清除空行
        List<Map<String, Object>> lists = (List<Map<String, Object>>) parameterMap.get("lists");
        List<Map<String, Object>> lists2 = new ArrayList<>();
        for (Map<String, Object> map : lists) {
            if (map.isEmpty() == false) {
                // 清理输入的空格和换行符\r\n\t
                var keys = map.keySet();
                for (String key : keys) {
                    map.put(key, Utils.clearStr((String) map.get(key)));
                }
                lists2.add(map);
            }
        }
        parameterMap.put("lists", lists2);
        //如果CATEGORY = "MAT" - 插入Details至MY_PLANNING_TODAY_TICKETS_MAT
        switch ((String) parameterMap.get("category")) {
            case "1. Master Data (Mat)" -> mptWorkspaceDao.saveMat(parameterMap);
            case "2. Master Data (Vend)" -> mptWorkspaceDao.saveVend(parameterMap);
            case "3. Sales Order" -> mptWorkspaceDao.saveSO(parameterMap);
            case "4. Purchasing Order" -> mptWorkspaceDao.savePO(parameterMap);
            case "5. Manufacturing Order" -> mptWorkspaceDao.saveMO(parameterMap);
            case "7. Inventory" -> mptWorkspaceDao.saveINV(parameterMap);
        }
    }

    private void saveLog(String id, String operator, String operation) {
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("id", id);
        parameterMap.put("operator", operator);
        parameterMap.put("operation", Utils.getStrByByteLength(operation, 512));
        mptWorkspaceDao.saveLog(parameterMap);
    }

    private void saveNotice(String id, String content, String createBy, String... receivers) {
        this.saveNotice(id, content, createBy, Arrays.asList(receivers));
    }

    private void saveNotice(String id, String content, String createBy, List<String> receivers) {
        if (receivers.isEmpty() == false) {
            Map<String, Object> parameterMap = new HashMap<>();
            parameterMap.put("id", id);
            parameterMap.put("content", content);
            parameterMap.put("createBy", createBy);
            parameterMap.put("receivers", receivers);
            mptWorkspaceDao.saveNotice(parameterMap);

            // send notice mail
            StringBuilder body = new StringBuilder();
            Map<String, Object> info = mptWorkspaceDao.queryTicketInfoById(parameterMap);
            MailBean mailBean = new MailBean();
            mailBean.setSubject("【DSS MPT Notice】" + content);
            List<String> to = new ArrayList<>();
            for (String r : receivers) {
                to.add(r + "@se.com");
            }
            mailBean.setTo(StringUtils.join(to, ","));

            List<String> cc = new ArrayList<>();
            cc.add("<EMAIL>");
            cc.add("<EMAIL>");
            mailBean.setCc(StringUtils.join(cc, ","));

            body.append("<div>");

            if (CODE_WAITING_FOR_APPROVAL.equals(info.get("STATUS")) && receivers.contains((String) info.get("APPROVER"))) {
                body.append("<p>");
                body.append("<a href='https://scp-dss.cn.schneider-electric.com/#/login_and_redirect?redirect=/diagnosis/my_planning_today/mpt_workspace/");
                body.append(id);
                body.append("' target=_blank>");
                body.append("<span style='font-size: 15pt;color: #409eff'>");
                body.append("Approve");
                body.append("</span></a>");
                body.append("&nbsp;&nbsp;or&nbsp;&nbsp;");
                body.append("<a href='https://scp-dss.cn.schneider-electric.com/#/login_and_redirect?redirect=/diagnosis/my_planning_today/mpt_workspace/");
                body.append(id);
                body.append("' target=_blank>");
                body.append("<span style='font-size: 15pt;color: #409eff'>");
                body.append("Reject");
                body.append("</span></a>");
                body.append("&nbsp;&nbsp;this ticket.");
                body.append("</p><br/>");
            } else if (CODE_WAITING_FOR_ACCEPTANCE.equals(info.get("STATUS")) && receivers.contains((String) info.get("ACTION_OWNER"))) {
                body.append("<p>");
                body.append("<a href='https://scp-dss.cn.schneider-electric.com/#/login_and_redirect?redirect=/diagnosis/my_planning_today/mpt_workspace/");
                body.append(id);
                body.append("' target=_blank>");
                body.append("<span style='font-size: 15pt;color: #409eff'>");
                body.append("Accept");
                body.append("</span></a>");
                body.append("&nbsp;&nbsp;or&nbsp;&nbsp;");
                body.append("<a href='https://scp-dss.cn.schneider-electric.com/#/login_and_redirect?redirect=/diagnosis/my_planning_today/mpt_workspace/");
                body.append(id);
                body.append("' target=_blank>");
                body.append("<span style='font-size: 15pt;color: #409eff'>");
                body.append("Reject");
                body.append("</span></a>");
                body.append("&nbsp;&nbsp;this ticket.");
                body.append("</p><br/>");
            } else {
                body.append("<p>");
                body.append("You have a new message in MPT, please click");
                body.append("<a href='https://scp-dss.cn.schneider-electric.com/#/login_and_redirect?redirect=/diagnosis/my_planning_today/mpt_workspace/");
                body.append(id);
                body.append("' target=_blank>");
                body.append("<b>");
                body.append(" here ");
                body.append("</b></a> to learn more.</p>");
                body.append("<br/>");
            }
            body.append("<table>");
            body.append("<tr><th>Title</th><td>").append(info.get("TITLE")).append("</td></tr>");
            body.append("<tr><th>Category</th><td>").append(info.get("CATEGORY")).append("</td></tr>");
            body.append("<tr><th>Subjects</th><td>").append(StringUtils.join(JSONArray.parseArray((String) info.get("SUBJECTS")), ", ")).append("</td></tr>");
            body.append("<tr><th>Active Period</th><td>").append(info.get("START_DATE")).append(" - ").append(info.get("END_DATE")).append("</td></tr>");
            body.append("<tr><th>Action Type</th><td>").append(info.get("ACTION")).append("</td></tr>");
            body.append("<tr><th>Brief Introduction</th><td>").append(info.get("CONTENT")).append("</td></tr>");
            body.append("</table>");
            mailBean.setDefaultStyle(true);
            mailBean.setBody(body.toString());
            mailFeignClient.sendAsync(mailBean);
        }
    }
    // endregion

    // region query tickets by status
    @Override
    public Response queryTicketsByStatus(Map<String, Object> parameterMap) {
        List<Map<String, String>> userList = mptWorkspaceDao.queryUserMapping();
        Map<String, String> userMapping = new HashMap<>();
        for (Map<String, String> user : userList) {
            userMapping.put(user.get("SESA_CODE"), user.get("USER_DESC"));
        }
        List<Map<String, Object>> dataList = mptWorkspaceDao.queryTicketsByStatus(parameterMap);
        for (Map<String, Object> data : dataList) {
            this.collspanField(data, "SUBJECTS", null);
            this.collspanField(data, "COLLABORATORS", userMapping);
            String actionOwner = (String) data.get("ACTION_OWNER");
            String requestBy = (String) data.get("REQUEST_BY");
            data.put("ACTION_OWNER", userMapping.getOrDefault(actionOwner, actionOwner));
            data.put("REQUEST_BY", userMapping.getOrDefault(requestBy, requestBy));
            String approver = (String) data.get("APPROVER");
            data.put("APPROVER", userMapping.getOrDefault(approver, approver));
        }
        return response.setBody(dataList);
    }

    @Override
    public Response queryTicketById(Map<String, Object> parameterMap, Session session) {
        Map<String, Object> info = mptWorkspaceDao.queryTicketInfoById(parameterMap);
        if (info == null || info.isEmpty()) {
            return response;
        }

        List<Map<String, String>> userList = mptWorkspaceDao.queryUserMapping();
        Map<String, Object> userMapping = new HashMap<>();
        for (Map<String, String> user : userList) {
            userMapping.put(user.get("SESA_CODE"), user.get("USER_DESC"));
        }

        Map<String, Object> resultMap = new HashMap<>();
        String approver = (String) info.get("APPROVER");
        String actionOwner = (String) info.get("ACTION_OWNER");
        info.put("SUBJECTS", "&minus; " + StringUtils.join(JSONArray.parseArray((String) info.get("SUBJECTS")), "<br/>&minus; "));
        List<String> collaborators = new ArrayList<>();
        if (info.get("COLLABORATORS") != null) {
            collaborators = JSONArray.parseArray((String) info.get("COLLABORATORS")).toJavaList(String.class);
        }
        info.put("COLLABORATORS", collaborators);
        info.put("CONTENT", Utils.clob2String(info.get("CONTENT")));
        info.put("CREATE_BY", userMapping.getOrDefault((String) info.get("CREATE_BY"), info.get("CREATE_BY")));
        info.put("APPROVER", userMapping.getOrDefault(approver, approver));
        info.put("ASSIGNED_BY_SESA", info.get("ASSIGNED_BY"));
        info.put("ASSIGNED_BY", userMapping.getOrDefault((String) info.get("ASSIGNED_BY"), info.get("ASSIGNED_BY")));
        info.put("ACTION_OWNER", userMapping.getOrDefault(actionOwner, actionOwner));
        info.put("ACTION_OWNER_SESA", actionOwner);
        resultMap.put("info", info);

        List<Map<String, Object>> logs = mptWorkspaceDao.queryTicketLogsById(parameterMap);
        for (Map<String, Object> log : logs) {
            log.put("OPERATOR", userMapping.getOrDefault((String) log.get("OPERATOR"), log.get("OPERATOR")));
        }
        resultMap.put("logs", logs);

        List<Map<String, Object>> replies = mptWorkspaceDao.queryTicketRepliesById(parameterMap);
        for (Map<String, Object> reply : replies) {
            String replier = (String) reply.get("REPLIER");
            reply.put("IS_ME", StringUtils.equalsIgnoreCase(replier, session.getUserid()));
            reply.put("REPLIER", userMapping.getOrDefault(replier, replier));
        }
        resultMap.put("replies", replies);

        parameterMap.put("CATEGORY", info.get("CATEGORY"));
        resultMap.put("details", mptWorkspaceDao.queryTicketDetailsById(parameterMap));

        Map<String, Object> handler = new HashMap<>();
        handler.put("IS_APPROVER", StringUtils.equalsIgnoreCase(approver, session.getUserid()) && CODE_WAITING_FOR_APPROVAL.equals(info.get("STATUS")));
        handler.put("IS_ACCEPTER", StringUtils.equalsIgnoreCase(actionOwner, session.getUserid()) && CODE_WAITING_FOR_ACCEPTANCE.equals(info.get("STATUS")));
        // 如果Action Type是4. Escalation, 并且Action Owner是当前登录用户, 则显示Escalation按钮
        handler.put("IS_MY_ESCALATION", StringUtils.equalsIgnoreCase(actionOwner, session.getUserid()) && "4. Escalation".equals(info.get("ACTION")));
        resultMap.put("handler", handler);
        return response.setBody(resultMap);
    }

    @Override
    public Response saveTicketComment(Map<String, Object> parameterMap, Session session) {
        mptWorkspaceDao.saveTicketComment(parameterMap);
        this.saveLog((String) parameterMap.get("id"), session.getUserid(), "Posted a commit");
        return response;
    }

    @Override
    public Response deleteTicketComment(Map<String, Object> parameterMap, Session session) {
        mptWorkspaceDao.deleteTicketComment(parameterMap);
        this.saveLog((String) parameterMap.get("id"), session.getUserid(), "Deleted a commit");
        return response;
    }

    @Override
    public Response approveTicketById(Map<String, Object> parameterMap, Session session) {
        Map<String, Object> info = mptWorkspaceDao.queryTicketInfoById(parameterMap);
        String state = (String) parameterMap.get("status");
        String operation;
        String notice;
        String category;
        switch (state) {
            case "approve":
                parameterMap.put("status", CODE_TAKING_EFFECT);
                operation = "Ticket Approved";
                category = "ticket";
                notice = "approved";
                break;
            case "accept":
                parameterMap.put("status", CODE_TAKING_EFFECT);
                operation = "Forward Accepted";
                category = "forward";
                notice = "accepted";
                break;
            case "refuse":
                parameterMap.put("status", CODE_REJECTED);
                operation = "Forward Refused";
                category = "forward";
                notice = "accepted";
                break;
            default:
                parameterMap.put("status", CODE_REJECTED);
                operation = "Ticket Rejected";
                category = "ticket";
                notice = "rejected";
        }

        String id = (String) info.get("ID");
        String title = (String) info.get("TITLE");
        String userid = session.getUserid();
        this.saveLog(id, userid, operation);
        this.saveNotice(id, "Your " + category + "【" + title + "】 has been " + notice, userid, (String) info.get("REQUEST_BY"));
        mptWorkspaceDao.approveTicketById(parameterMap);
        return response;
    }

    @Override
    public Response closeTicketById(Map<String, Object> parameterMap, Session session) {
        Map<String, Object> info = mptWorkspaceDao.queryTicketInfoById(parameterMap);
        String userid = session.getUserid(); // 关闭Ticket的人
        String id = (String) info.get("ID");
        String title = (String) info.get("TITLE");
        this.saveLog(id, userid, "Ticket Closed");
        this.saveNotice(id, "Your ticket【" + title + "】 has been closed", userid, (String) info.get("REQUEST_BY"));
        mptWorkspaceDao.closeTicketById(parameterMap);
        return response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response updateCollaborators(Map<String, Object> parameterMap, Session session) {
        String orgCollaborators = mptWorkspaceDao.getCollaboratorsById(parameterMap);
        if (StringUtils.isBlank(orgCollaborators)) {
            orgCollaborators = "[]";
        }
        List<Map<String, String>> userList = mptWorkspaceDao.queryUserMapping();
        Map<String, Object> userMapping = new HashMap<>();
        for (Map<String, String> user : userList) {
            userMapping.put(user.get("SESA_CODE"), user.get("USER_NAME"));
        }
        List<String> orgCollaboratorList = JSONArray.parseArray(orgCollaborators, String.class);
        String orgCollaboratorsStr = StringUtils.join(orgCollaboratorList.stream().map(e -> userMapping.getOrDefault(e, e)).toArray(), ", ");

        List<Object> collaborators = (List<Object>) parameterMap.get("collaborators");
        List<String> users = new ArrayList<>();
        for (Object temp : collaborators) {
            if (temp instanceof List) {
                users.add(((List<String>) temp).get(1));
            } else if (temp instanceof String) {
                users.add((String) temp);
            }
        }
        String newCollaborators = JSON.toJSONString(users);
        String newCollaboratorsStr = StringUtils.join(users.stream().map(e -> userMapping.getOrDefault(e, e)).toArray(), ", ");
        this.saveLog((String) parameterMap.get("id"), session.getUserid(), "Updated collaborators from '" + orgCollaboratorsStr + "' to '" + newCollaboratorsStr + "'");
        parameterMap.put("collaborators", newCollaborators);
        mptWorkspaceDao.updateCollaborators(parameterMap);
        return response;
    }

    @Override
    public Response updateActionPeriod(Map<String, Object> parameterMap, Session session) {
        Map<String, String> orgActionPeriod = mptWorkspaceDao.getActionPeriodById(parameterMap);
        if (orgActionPeriod == null || orgActionPeriod.isEmpty()) {
            throw new RuntimeException("Cannot find any ticket by id " + parameterMap.get("id"));
        }

        JSONArray array = (JSONArray) parameterMap.get("dateRange");

        String log = "Updated action period from '" + orgActionPeriod.get("START_DATE") + " - " + orgActionPeriod.get("END_DATE") + "' to '" + array.get(0) + " - " + array.get(1) + "'";
        this.saveLog((String) parameterMap.get("id"), session.getUserid(), log);
        mptWorkspaceDao.updateActionPeriod(parameterMap);
        return response;
    }

    @Override
    public Response updateActionOwner(Map<String, Object> parameterMap, Session session) {
        String log = "Updated action owner from " + parameterMap.get("fromOwnerName") + " to " + parameterMap.get("toOwnerName");
        this.saveLog((String) parameterMap.get("id"), session.getUserid(), log);
        mptWorkspaceDao.updateActionOwner(parameterMap);
        return response;
    }


    @Override
    public Response queryQuickAccess(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("approval", mptWorkspaceDao.queryQuickAccessApproval(parameterMap));
        resultMap.put("forward", mptWorkspaceDao.queryQuickAccessForward(parameterMap));
        resultMap.put("escalation", mptWorkspaceDao.queryQuickAccessEscalation(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    public Response queryLatestNews(Map<String, Object> parameterMap) {
        return response.setBody(mptWorkspaceDao.queryLatestNews(parameterMap));
    }

    private void collspanField(Map<String, Object> data, String key, Map<String, String> mapping) {
        String field = (String) data.get(key);
        if (StringUtils.isBlank(field)) {
            data.put(key, "");
            data.put(key + "_TITLE", "-");
            return;
        }
        String result;
        String title;
        try {
            JSONArray array = JSONArray.parseArray(field);
            if (array.size() == 0) {
                result = "";
                title = result;
            } else if (array.size() == 1) {
                result = String.valueOf(array.get(0));
                if (mapping != null) {
                    result = mapping.getOrDefault(result, result);
                }
                title = result;
            } else {
                result = String.valueOf(array.get(0));
                if (mapping != null) {
                    result = mapping.getOrDefault(result, result);
                    title = StringUtils.join(array.stream().map(e -> mapping.getOrDefault(String.valueOf(e), String.valueOf(e))).toArray(), "<br>");
                } else {
                    title = "&minus; " + StringUtils.join(array, "<br>&minus; ");
                }
                result = result + " <span style='font-size:80%;font-weight: bold'>+" + (array.size() - 1) + "</span>";
            }
        } catch (Exception ignore) {
            result = field;
            title = result;
        }
        data.put(key, result);
        data.put(key + "_TITLE", title);
    }
    // region

}
