<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.starter.login.dao.ILoginDao">
	<select id="login" parameterType="java.util.Map" resultType="com.starter.login.bean.Session">
		select sesa_code as userid,
		       user_name as username,
		       lower(email) as email,
		       ENTITY_NAME as entity,
		       IS_MAINTAINER as maintainer,
		       IS_SUPERVISOR as supervisor
		  from SY_USER_MASTER_DATA
		 where lower(sesa_code) = lower(#{username,jdbcType=VARCHAR})
		   and status = 'Active'
		 	   <if test="username != 'guest'.toString()">
		       		and password = #{password,jdbcType=VARCHAR}
			   </if>
		 fetch first 1 rows only
	</select>

	<select id="loginWithoutPWD" resultType="com.starter.login.bean.Session">
		select sesa_code as userid,
		       user_name as username,
		       lower(email) as email,
		       <PERSON><PERSON><PERSON><PERSON>_NAME as entity,
		       <PERSON>_MAINTAINER as maintainer,
		       <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as supervisor
		  from SY_USER_MASTER_DATA
		 where lower(sesa_code) = lower(#{username,jdbcType=VARCHAR})
		   and status = 'Active'
		 fetch first 1 rows only
	</select>

	<select id="queryAuthedMenu" parameterType="java.lang.String" resultType="java.util.HashMap">
		WITH SUPERVISOR_AUTH AS (
			SELECT MENU_CODE FROM SY_MENU T WHERE T.PARENT_ID = 'SCP'
			UNION ALL
			SELECT MENU_CODE FROM SY_MENU T WHERE T.PARENT_ID IN (SELECT MENU_CODE FROM SY_MENU T WHERE T.PARENT_ID = 'SCP')
		 ),
		 MENU AS (SELECT CASE WHEN T.MENU_CODE IN (SELECT SA.MENU_CODE FROM  SUPERVISOR_AUTH SA) AND TT.IS_SUPERVISOR = 'Y' THEN 'public'
							  WHEN T2.ACCESSIBLE = 'false' THEN 'private'
							  WHEN T2.ACCESSIBLE = 'true' THEN 'public'
							  ELSE T.CLASSIFICATION END AS CLASSIFICATION,
							 T.URL,
							 T.NAME,
							 T.MENU_TYPE,
							 T2.AUTH_DETAILS,
                             T3.NAME AS PARENT_NAME
					  FROM SY_MENU T
							   LEFT JOIN SY_MENU_AUTH T2 ON T.MENU_CODE = T2.MENU_CODE AND T2.USER_ID = #{userid,jdbcType=VARCHAR}
							   LEFT JOIN SY_MENU T3 ON T.PARENT_ID = T3.MENU_CODE
							   CROSS JOIN (SELECT T3.IS_SUPERVISOR FROM SY_USER_MASTER_DATA T3 WHERE T3.SESA_CODE = #{userid,jdbcType=VARCHAR}) TT
					  <if test="specificUrl != null and specificUrl != ''.toString()">
                          WHERE T.URL = #{specificUrl,jdbcType=VARCHAR}
                      </if>
	     )
		SELECT T.URL, T.MENU_TYPE, T.NAME, T.AUTH_DETAILS, T.PARENT_NAME
		FROM MENU T
		WHERE T.CLASSIFICATION = 'public'
	</select>

	<select id="queryUserPassword" parameterType="java.util.Map" resultType="java.lang.String">
		select password from SY_USER_MASTER_DATA where lower(sesa_code) = lower(#{session.userid,jdbcType=VARCHAR})
	</select>

	<update id="updateUserPassword" parameterType="java.util.Map">
		update SY_USER_MASTER_DATA
		set password = #{newPassword,jdbcType=VARCHAR},
			update_date$ = sysdate,
			update_by$ = #{session.userid,jdbcType=VARCHAR}
		where lower(sesa_code) = lower(#{session.userid,jdbcType=VARCHAR})
	</update>

	<update id="updateUserPasswordMD5">
		update SY_USER_MASTER_DATA
		set password = #{password,jdbcType=VARCHAR},
			update_date$ = sysdate,
			update_by$ = #{username,jdbcType=VARCHAR}
		where upper(sesa_code) = upper(#{username,jdbcType=VARCHAR})
	</update>

    <insert id="saveMethodAuth" parameterType="java.util.List">
		begin
		insert into sy_menu_temporary (menu_code,url,order_no,parent_id,classification,menu_type)
		<foreach collection="list" index="index" item="item" open="" separator="union all" close="">
			select #{item.menuCode,jdbcType=VARCHAR},#{item.url,jdbcType=VARCHAR},0,#{item.parent,jdbcType=VARCHAR},#{item.classification,jdbcType=VARCHAR},'method' from dual
		</foreach>;

		delete from SY_MENU t where t.menu_code not in (select menu_code from sy_menu_temporary) and t.menu_type = 'method' and t.create_date$ &lt; sysdate - 15;

		merge into SY_MENU t
		using (select * from sy_menu_temporary) s on (t.menu_code = s.menu_code)
		when matched then
		update set t.classification = s.classification,t.parent_id = s.parent_id, t.update_date$ = sysdate, t.update_by$ = 'root'
		when not matched then
		insert (menu_code,url,order_no,parent_id,classification,menu_type, create_date$, create_by$)
		values (s.menu_code,s.url,s.order_no,s.parent_id,s.classification,s.menu_type, sysdate, 'root');
		end;
	</insert>

	<select id="queryMenuList" parameterType="java.lang.String" resultType="java.util.HashMap">
		WITH SUPERVISOR_AUTH AS (
			SELECT MENU_CODE FROM SY_MENU T WHERE T.PARENT_ID = 'SCP'
			UNION ALL
			SELECT MENU_CODE FROM SY_MENU T WHERE T.PARENT_ID IN (SELECT MENU_CODE FROM SY_MENU T WHERE T.PARENT_ID = 'SCP')
		),
		MENU AS (SELECT  T.MENU_CODE,
						 T.PARENT_ID,
						 CASE WHEN T.MENU_CODE IN (SELECT SA.MENU_CODE FROM  SUPERVISOR_AUTH SA) AND TT.IS_SUPERVISOR = 'Y' THEN 'public'
							  WHEN T2.ACCESSIBLE = 'false' THEN 'private'
							  WHEN T2.ACCESSIBLE = 'true' THEN 'public'
							  ELSE T.CLASSIFICATION END AS CLASSIFICATION,
						 T.URL,
						 T.NAME,
						 T.ICO_CLS,
						 T.ORDER_NO
					  FROM SY_MENU T
							   LEFT JOIN SY_MENU_AUTH T2 ON T.MENU_CODE = T2.MENU_CODE AND T2.USER_ID = #{_parameter,jdbcType=VARCHAR}
							   CROSS JOIN (SELECT T3.IS_SUPERVISOR FROM SY_USER_MASTER_DATA T3 WHERE T3.SESA_CODE = #{_parameter,jdbcType=VARCHAR}) TT
					  WHERE T.MENU_TYPE = 'menu')
		SELECT T.MENU_CODE, T.PARENT_ID, T.CLASSIFICATION, T.URL, T.NAME, T.ICO_CLS, T2.NAME AS PARENT_NAME
		FROM MENU T
				 LEFT JOIN SY_MENU T2
							ON T.PARENT_ID = T2.MENU_CODE
		WHERE T.CLASSIFICATION = 'public'
		ORDER BY T.ORDER_NO
 	</select>

    <select id="querySystemNotification" resultType="java.util.Map">
		SELECT MESSAGE, TYPE
		  FROM SY_NOTIFICATION
		 WHERE SYSDATE BETWEEN
			   TO_DATE(TO_CHAR(START_DATE, 'YYYY/MM/DD') || ' ' || START_TIME, 'YYYY/MM/DD HH24:MI')
			   AND TO_DATE(TO_CHAR(END_DATE, 'YYYY/MM/DD') || ' ' || END_TIME, 'YYYY/MM/DD HH24:MI')
		       AND ENABLE = 'Y'
	</select>

	<select id="queryScpHotMenus" resultType="com.starter.login.bean.Menu">
		SELECT T.NAME, T.ICO_CLS AS ICO, T.URL
		FROM SY_MENU T
				 LEFT JOIN (
			SELECT T.URL, COUNT(1) CNT
			FROM SY_VISIT_LOGS T
			WHERE T.USERID = UPPER(#{username,jdbcType=VARCHAR})
			  AND T.VISIT_TIME BETWEEN SYSDATE - 60 AND SYSDATE
			GROUP BY T.URL
		) MM ON T.URL = MM.URL
		WHERE T.MENU_TYPE = 'menu'
		ORDER BY NVL(MM.CNT, 0) DESC
		OFFSET 0 ROWS FETCH NEXT 10 ROWS ONLY
	</select>

	<insert id="saveLoginLogs" parameterType="java.util.Map">
		insert into sy_login_logs(userid, login_ip, login_time) values (upper(#{username,jdbcType=VARCHAR}), #{ip,jdbcType=VARCHAR}, sysdate)
	</insert>

	<insert id="saveVisitLogs" parameterType="java.util.Map">
		insert into sy_visit_logs(url, userid, login_ip, visit_time, response_code)
		values
		(#{url,jdbcType=VARCHAR}, upper(#{username,jdbcType=VARCHAR}), #{ip,jdbcType=VARCHAR}, sysdate, #{responseCode,jdbcType=VARCHAR})
	</insert>

	<update id="sendToHomepage">
		MERGE INTO SY_USER_HOMEPAGE T
		USING (
			SELECT upper(#{session.userid,jdbcType=VARCHAR}) USER_ID,
			       #{url,jdbcType=VARCHAR} HOMEPAGE
			  FROM DUAL
		) S ON (T.USER_ID = S.USER_ID)
		WHEN MATCHED THEN
		UPDATE SET T.HOMEPAGE = S.HOMEPAGE, T.UPDATE_BY$ = S.USER_ID, T.UPDATE_DATE$ = SYSDATE
		WHEN NOT MATCHED THEN
		INSERT (USER_ID, HOMEPAGE, CREATE_BY$, CREATE_DATE$)
		VALUES (S.USER_ID, S.HOMEPAGE, S.USER_ID, SYSDATE)
	</update>

	<select id="queryUserHomepage" resultType="java.lang.String">
		SELECT COALESCE(T.HOMEPAGE, MM.PROPERTY, '/demand/tracking')
		FROM SY_USER_HOMEPAGE T
				 INNER JOIN (
			SELECT T2.PROPERTY
			FROM SY_PROPERTIES T2
			WHERE T2.NAME = 'DEFAULT_HOMEPAGE'
		) MM ON 1 = 1
		WHERE T.USER_ID = upper(#{userid,jdbcType=VARCHAR})
	</select>

    <select id="queryAvailableEntity" resultType="java.util.Map">
		SELECT ENTITY_NAME, "GROUP" FROM SY_USER_MASTER_DATA_ENTITY_V
	</select>

	<select id="queryUserInfo" resultType="java.util.Map">
		SELECT SESA_CODE, USER_NAME, EMAIL, JOB_CODE, LINE_MANAGER, JOB_TITLE
		FROM UM3_SPICE_USER_DATA_V T
		WHERE T.SESA_CODE = upper(#{userid,jdbcType=VARCHAR})
	</select>

	<select id="queryExistAccount" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM SY_USER_MASTER_DATA T WHERE T.SESA_CODE = UPPER(#{sesaNo, jdbcType=VARCHAR})
	</select>

    <select id="queryAllMenuAndUrl" resultType="java.util.Map">
		SELECT T.URL, T.NAME AS MENU_NAME, DECODE(P.NAME, 'SubMenu', '', P.NAME) AS PARENT_NAME
		FROM SY_MENU T
				 LEFT JOIN SY_MENU P ON T.PARENT_ID = P.MENU_CODE
		WHERE T.MENU_TYPE != 'method' AND P.MENU_TYPE != 'method'
		AND T.URL IS NOT NULL
	</select>

    <insert id="createNewAccount">
		INSERT INTO SY_USER_MASTER_DATA
		(SESA_CODE, USER_NAME, JOB_TITLE, JOB_CODE, EMAIL, LINE_MANAGER_1, BACK_UP_1, ENTITY_NAME, CREATE_BY$, CREATE_DATE$, STATUS)
		VALUES
		(UPPER(#{sesaNo, jdbcType=VARCHAR}), #{name, jdbcType=VARCHAR}, #{jobTitle, jdbcType=VARCHAR},
		 UPPER(#{jobCode, jdbcType=VARCHAR}), LOWER(#{email, jdbcType=VARCHAR}), UPPER(#{lineManagerSesaNo, jdbcType=VARCHAR}), UPPER(#{backupSesaNo, jdbcType=VARCHAR}),
		 #{entity, jdbcType=VARCHAR}, UPPER(#{sesaNo, jdbcType=VARCHAR}), SYSDATE, 'New Register')
	</insert>

	<select id="querySCPATabCols" resultType="java.util.Map">
		SELECT T.TABLE_NAME, T.COLUMN_NAME, T.DATA_TYPE FROM USER_TAB_COLS T WHERE T.DATA_TYPE IN ('DATE', 'CHAR', 'NUMBER', 'FLOAT', 'VARCHAR2')
	</select>
</mapper>
