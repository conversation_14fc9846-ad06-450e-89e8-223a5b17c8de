package com.starter.context.configuration.database.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.starter.context.configuration.database.DatabaseType;

/**
 * ClickHouse数据源配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "spring.datasource.clickhouse")
public class DruidPropertiesClickHouse extends BaseDruidProperties {
    private String url;
    private String username;
    private String password;

    @Override
    public boolean isDefaultTargetDataSource() {
        return false;
    }

    @Override
    public DatabaseType getDataBaseType() {
        return DatabaseType.CLICKHOUSE;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
