package com.starter.context.configuration;

import com.starter.context.SpringContext;
import com.starter.utils.Utils;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.MqttTopic;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import java.util.Map;
import java.util.Objects;

@Configuration
@IntegrationComponentScan
@ConditionalOnProperty(name = "spring.mqtt", havingValue = "true", matchIfMissing = false)
public class MqttConfiguration {

    private final static String HOST = "tcp://***********:61616";

    private final static String USERNAME = "anonymous";

    private final String LISTEN_TOPIC = "scp/dss/#";

    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{HOST});
        options.setUserName(USERNAME);
        options.setCleanSession(true);
        factory.setConnectionOptions(options);
        return factory;
    }

    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }

    @Bean
    public MessageProducer inbound() {
        String clientId = "scp-service-inbound-" + Utils.randomStr(4);
        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(clientId, mqttClientFactory(), LISTEN_TOPIC);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(0);
        adapter.setOutputChannel(mqttInputChannel());
        System.out.println("mqtt handler loaded!");
        return adapter;
    }

    @Bean
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public MessageHandler handler() {
        return message -> {
            ApplicationContext context = SpringContext.getApplicationContext();
            Map<String, IMqttMessageHandler> messageMap = context.getBeansOfType(IMqttMessageHandler.class);

            for (IMqttMessageHandler messageHandler : messageMap.values()) {
                String topic = Objects.requireNonNull(message.getHeaders().get("mqtt_receivedTopic")).toString();
                messageHandler.mqttMessageHandler(topic, message.getPayload());
            }
        };
    }

    private static MqttClient mqttClient = null;
    private static MqttConnectOptions mqttConnectOptions = null;

    private static MqttConnectOptions getMqttConnectOptions() {
        if (mqttConnectOptions == null) {
            mqttConnectOptions = new MqttConnectOptions();
            mqttConnectOptions.setCleanSession(true);
            mqttConnectOptions.setConnectionTimeout(30);
            mqttConnectOptions.setUserName(USERNAME);
        }
        return mqttConnectOptions;
    }

    public static void publishMessage(String topic, String message) {
        try {
            if (message == null) {
                message = "";
            }
            if (mqttClient == null) {
                String clientId = "scp-service-outbound-" + Utils.randomStr(4);
                MemoryPersistence memoryPersistence = new MemoryPersistence();
                mqttClient = new MqttClient(HOST, clientId, memoryPersistence);
            }
            synchronized (MqttConfiguration.class) {
                if (mqttClient.isConnected() == false) {
                    mqttClient.connect(getMqttConnectOptions());
                }
            }
            MqttMessage mqttMessage = new MqttMessage();
            mqttMessage.setQos(0);
            mqttMessage.setPayload(message.getBytes());

            MqttTopic mqttTopic = mqttClient.getTopic(topic);
            mqttTopic.publish(mqttMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
