<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.rag.dao.IMossQuickLookDao">

	<select id="queryAllAvalibleTables" resultType="java.util.Map">
		SELECT OBJ_NAME, OBJ_TYPE FROM SCPM.QUICKLOOK_INDEX_V T WHERE T.OBJ_TYPE = 'TABLE' ORDER BY OBJ_NAME
	</select>

    <select id="queryAllAvalibleColumns" resultType="java.util.Map">
		SELECT OBJ_NAME, OBJ_TYPE FROM SCPM.QUICKLOOK_INDEX_V T WHERE T.OBJ_TYPE = 'COLUMN' ORDER BY OBJ_NAME
	</select>

    <select id="queryTableInfo" resultType="java.util.Map">
		SELECT OBJECT_NAME, OBJECT_TYPE, COMMENTS, TO_CHAR(NVL(T.UPDATE_DATE$, T.CREATE_DATE$), 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME, USER_NAME AS CREATE_BY
		FROM SCPM.TAB_COL_COMMENTS T LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON NVL(T.UPDATE_BY$, T.CREATE_BY$) = T2.SESA_CODE
		WHERE T.OBJECT_NAME = #{tableName, jdbcType=VARCHAR} AND T.OBJECT_TYPE = 'TABLE'
		FETCH NEXT 1 ROWS ONLY
	</select>

    <select id="queryTablesByColumnName" resultType="java.util.Map">
		SELECT T.TABLE_NAME
		 FROM ALL_TAB_COLS T
		WHERE T.COLUMN_NAME = #{columnName, jdbcType=VARCHAR}
		  AND T.TABLE_NAME NOT LIKE '%$%' AND T.TABLE_NAME NOT LIKE 'PRE_DEL_%'
		  AND T.OWNER = 'SCPM'
		ORDER BY T.SEGMENT_COLUMN_ID
	</select>

    <select id="queryTableNameByColumnName" resultType="java.util.Map">
		SELECT T.TABLE_NAME, T.DATA_TYPE, T.COLUMN_NAME
		 FROM ALL_TAB_COLS T
		WHERE T.COLUMN_NAME = #{objectName, jdbcType=VARCHAR}
		  AND T.OWNER = 'SCPM'
		FETCH NEXT 1 ROWS ONLY
	</select>

    <select id="queryColumnValueName" resultType="java.lang.String">
		SELECT
		<choose>
            <when test="DATA_TYPE == 'VARCHAR2'.toString() or DATA_TYPE == 'CHAR'.toString()">
                 T."${COLUMN_NAME}"
            </when>
            <when test="DATA_TYPE == 'DATE'.toString()">
                TO_CHAR(T."${COLUMN_NAME}", 'YYYY/MM/DD')
            </when>
            <when test="DATA_TYPE == 'NUMBER'.toString()">
                TO_CHAR(T."${COLUMN_NAME}")
            </when>
            <otherwise>
                'No Suggestion'
            </otherwise>
        </choose> AS VAL
		 FROM SCPM.${TABLE_NAME} T
		 WHERE T."${COLUMN_NAME}" IS NOT NULL
		 GROUP BY
		 <choose>
            <when test="DATA_TYPE == 'VARCHAR2'.toString() or DATA_TYPE == 'CHAR'.toString()">
                 T."${COLUMN_NAME}"
            </when>
            <when test="DATA_TYPE == 'DATE'.toString()">
                TO_CHAR(T."${COLUMN_NAME}", 'YYYY/MM/DD')
            </when>
            <when test="DATA_TYPE == 'NUMBER'.toString()">
                TO_CHAR(T."${COLUMN_NAME}")
            </when>
            <otherwise>
                'No Suggestion'
            </otherwise>
         </choose>
		 ORDER BY COUNT(1) DESC
		FETCH NEXT 20 ROWS ONLY
	</select>

     <select id="queryColumnsByTableName" resultType="java.util.Map">
        SELECT T.TABLE_NAME,
               T.COLUMN_NAME,
               T.DATA_TYPE,
               T.DATA_LENGTH,
               T2.COMMENTS,
               T2.BUSINESS_MEANING,
               T2.VALUE_EXAMPLE,
               T2.ALTERNATE_TERM,
               UMD.USER_NAME AS CREATE_BY,
               TO_CHAR(NVL(T2.UPDATE_DATE$, T2.CREATE_DATE$), 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME
        FROM ALL_TAB_COLS T LEFT JOIN SCPM.TAB_COL_COMMENTS T2 ON T.COLUMN_NAME = T2.OBJECT_NAME AND T2.OBJECT_TYPE = 'COLUMN'
                            LEFT JOIN SCPA.SY_USER_MASTER_DATA UMD ON NVL(T2.UPDATE_BY$, T2.CREATE_BY$) = UMD.SESA_CODE
        WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR}
          AND T.OWNER = 'SCPM'
        ORDER BY T.SEGMENT_COLUMN_ID
    </select>

    <select id="queryCommentsByColumnName" resultType="java.lang.String">
		SELECT T.COMMENTS
		FROM SCPM.TAB_COL_COMMENTS T
		WHERE T.OBJECT_NAME = #{columnName, jdbcType=VARCHAR}
		  AND T.OBJECT_TYPE = 'COLUMN'
	</select>

    <select id="queryObjectInfo" resultType="java.util.Map">
        SELECT OBJECT_NAME,
               OBJECT_TYPE,
               COMMENTS,
               VALUE_EXAMPLE,
               ALTERNATE_TERM,
               BUSINESS_MEANING
          FROM SCPM.TAB_COL_COMMENTS T
         WHERE T.OBJECT_TYPE = #{objectType, jdbcType=VARCHAR} AND T.OBJECT_NAME = #{objectName, jdbcType=VARCHAR}
    </select>

	<select id="saveComments">
		DECLARE
        	CLOB_CONTENT CLOB := #{comments, jdbcType=CLOB};
		BEGIN
			 MERGE INTO SCPM.TAB_COL_COMMENTS T
			 USING (SELECT #{objectName, jdbcType=VARCHAR} OBJECT_NAME, #{objectType, jdbcType=VARCHAR} OBJECT_TYPE FROM DUAL) S
			    ON (T.OBJECT_NAME = S.OBJECT_NAME AND T.OBJECT_TYPE = S.OBJECT_TYPE)
			WHEN MATCHED THEN
				UPDATE SET T.COMMENTS = CLOB_CONTENT,
				           T.BUSINESS_MEANING = #{businessMeaning, jdbcType=VARCHAR},
				           T.VALUE_EXAMPLE = #{valueExample, jdbcType=VARCHAR},
				           T.ALTERNATE_TERM = #{alternateTerm, jdbcType=VARCHAR},
				           T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
				           T.UPDATE_DATE$ = SYSDATE
			WHEN NOT MATCHED THEN
				INSERT (OBJECT_NAME, OBJECT_TYPE, COMMENTS, CREATE_BY$, CREATE_DATE$,
				        BUSINESS_MEANING, VALUE_EXAMPLE, ALTERNATE_TERM)
				VALUES (S.OBJECT_NAME, S.OBJECT_TYPE, CLOB_CONTENT, #{session.userid, jdbcType=VARCHAR}, SYSDATE,
                        #{businessMeaning, jdbcType=VARCHAR}, #{valueExample, jdbcType=VARCHAR}, #{alternateTerm, jdbcType=VARCHAR});
		END;
	</select>

    <select id="saveHistComments">
        DECLARE
            CLOB_CONTENT CLOB := #{comments, jdbcType=CLOB};
        BEGIN
            INSERT INTO SCPM.TAB_COL_COMMENTS_HIST
            (OBJECT_NAME, OBJECT_TYPE, COMMENTS, CREATE_BY$, CREATE_DATE$,
             BUSINESS_MEANING, VALUE_EXAMPLE, ALTERNATE_TERM)
            VALUES
            (#{objectName, jdbcType=VARCHAR}, #{objectType, jdbcType=VARCHAR}, CLOB_CONTENT, #{session.userid, jdbcType=VARCHAR}, SYSDATE,
             #{businessMeaning, jdbcType=VARCHAR},
             #{valueExample, jdbcType=VARCHAR}, #{alternateTerm, jdbcType=VARCHAR});
        END;
    </select>

	<select id="queryHistComments" resultType="java.util.Map">
		SELECT T.OBJECT_NAME,
		       T.BUSINESS_MEANING,
		       T.VALUE_EXAMPLE,
		       T.ALTERNATE_TERM,
		       T.COMMENTS,
		       TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME ,
		       T2.USER_NAME AS CREATE_BY
		FROM SCPM.TAB_COL_COMMENTS_HIST T LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON T.CREATE_BY$ = T2.SESA_CODE
		WHERE T.OBJECT_NAME = #{objectName, jdbcType=VARCHAR} AND T.OBJECT_TYPE = #{objectType, jdbcType=VARCHAR}
		ORDER BY CREATE_TIME DESC
	</select>

	<select id="queryKeywords" resultType="java.util.Map">
		SELECT VAL, LABEL FROM (
		<foreach collection="list" item="keywords" separator="UNION ALL" index="index">
		    SELECT OBJ_NAME AS VAL, OBJ_TYPE AS LABEL, ${index} PRORITY
		      FROM SCPM.QUICKLOOK_INDEX_V
		     WHERE LOWER(OBJ_NAME) LIKE '%' || LOWER(#{keywords,jdbcType=VARCHAR}) || '%'
		</foreach>
		) GROUP BY VAL, LABEL
		ORDER BY MIN(PRORITY), COUNT(1) DESC,
		CASE
			WHEN LABEL = 'TABLE' THEN 1
			WHEN LABEL = 'VIEW' THEN 2
			WHEN LABEL = 'MVIEW' THEN 3
			WHEN LABEL = 'COLUMN' THEN 4
			ELSE 6
		END
		FETCH NEXT 100 ROWS ONLY
	</select>

	<select id="queryCommentsTemplate" resultType="java.lang.Object">
		SELECT T.CONTENT FROM SY_DOCUMENTATION T WHERE T.DOC_ID = 'f4490c61b119'
	</select>
</mapper>
