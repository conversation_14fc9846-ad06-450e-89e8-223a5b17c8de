<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.rag.dao.ISQLTemplateDao">
	<sql id="queryReport1SQL">
		${answer}
	</sql>

	<select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryTableList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT *
        FROM (SELECT T.TEMPLATE_ID                AS KEY,
                     T.QUESTION                   AS LABEL,
                     T.TEMPLATE_GROUP             AS GROUPS
              FROM SCPM.SQL_TEMPLATE T) TT
        ORDER BY TT.GROUPS, TT.LABEL
    </select>

    <select id="queryTables" resultType="java.lang.String">
        SELECT DISTINCT T.TABLE_NAME FROM ALL_TABLES T WHERE T.OWNER = 'SCPM'
    </select>

    <select id="queryTableCols" resultType="java.lang.String">
        SELECT DISTINCT UPPER(T.COLUMN_NAME) FROM ALL_TAB_COLS T WHERE REGEXP_LIKE(t.COLUMN_NAME, '^(\w+)$') AND T.OWNER = 'SCPM'
    </select>

    <select id="queryTableOptions" resultType="java.lang.String">
        SELECT DISTINCT OBJ_NAME
        FROM SCPM.QUICKLOOK_INDEX_V
        WHERE OBJ_TYPE != 'COLUMN'
    </select>

    <select id="queryTemplateSQL" resultType="java.util.Map">
        SELECT *
        FROM SCPM.SQL_TEMPLATE T
        WHERE T.TEMPLATE_ID = ${templateID}
    </select>

    <insert id="saveNewTemplate">
        INSERT INTO SCPM.SQL_TEMPLATE (QUESTION, ANSWER, TEMPLATE_GROUP, CREATE_BY$, CREATE_DATE$)
        VALUES
            (#{question, jdbcType=VARCHAR}, #{answer, jdbcType=VARCHAR}, #{templateGroup, jdbcType=VARCHAR},
             #{session.userid, jdbcType=VARCHAR}, sysdate)
    </insert>

    <delete id="deleteTemplate">
        DELETE
        FROM SCPM.SQL_TEMPLATE
        WHERE TEMPLATE_ID = ${key}
    </delete>

    <update id="updateTemplate">
        UPDATE SCPM.SQL_TEMPLATE
        SET QUESTION       = #{question, jdbcType=VARCHAR},
            ANSWER         = #{answer, jdbcType=VARCHAR},
            TEMPLATE_GROUP = #{templateGroup, jdbcType=VARCHAR},
            UPDATE_BY$     = #{session.userid, jdbcType=VARCHAR},
            UPDATE_DATE$   = SYSDATE
        WHERE TEMPLATE_ID = ${templateID}
    </update>

</mapper>
