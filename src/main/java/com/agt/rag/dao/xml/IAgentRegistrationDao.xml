<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.rag.dao.IAgentRegistrationDao">

	<select id="queryExistsGroup" resultType="java.lang.String">
		SELECT DISTINCT T.GROUPS
		FROM SCPA.MOSS_AGENT_REGISTRATION T
		WHERE T.GROUPS IS NOT NULL
		ORDER BY T.GROUPS
	</select>

	<select id="queryApiIntroduction" resultType="java.lang.String">
		SELECT CONTENT FROM SCPA.PROMPT_TEMPLATE WHERE PROMPT_ID = '35dc61384d9a'
	</select>

	<insert id="saveAgent">
		INSERT INTO MOSS_AGENT_REGISTRATION(AGENT_ID, AGENT_TYPE, SUBJECT, GROUPS, CONTENT, REQUEST_URL, REQUEST_HEADER, REQUEST_BODY, CREATE_BY$, CREATE_DATE$, WELCOME_MSG)
		VALUES
		(#{AGENT_ID,jdbcType=VARCHAR}, #{AGENT_TYPE,jdbcType=VARCHAR}, #{SUBJECT,jdbcType=VARCHAR}, #{GROUPS,jdbcType=VARCHAR}, #{CONTENT,jdbcType=VARCHAR},
		#{REQUEST_URL,jdbcType=VARCHAR}, #{REQUEST_HEADER,jdbcType=VARCHAR}, #{REQUEST_BODY,jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, SYSDATE, #{WELCOME_MSG, jdbcType=VARCHAR})
	</insert>

	<select id="queryAgentList" resultType="com.scp.toolbox.bean.TreeData">
		SELECT T.AGENT_ID                                       AS KEY,
			   T.SUBJECT                                        AS LABEL,
			   T.GROUPS                                         AS GROUPS
		FROM MOSS_AGENT_REGISTRATION T
		ORDER BY GROUPS NULLS FIRST, LABEL
	</select>

	<select id="queryAgentWithId" resultType="java.util.HashMap">
		SELECT T.AGENT_ID,
		       T.AGENT_TYPE,
		       T.SUBJECT,
		       T.GROUPS,
		       T.SUBJECT,
		       T.CONTENT,
		       T.REQUEST_URL,
		       T.REQUEST_HEADER,
		       T.REQUEST_BODY,
		       UMD.USER_NAME AS CREATE_BY,
		       TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE,
		       UMD2.USER_NAME AS UPDATE_BY,
		       TO_CHAR(T.UPDATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') UPDATE_DATE,
		       T.WELCOME_MSG
		FROM MOSS_AGENT_REGISTRATION T
			 LEFT JOIN SY_USER_MASTER_DATA UMD ON T.CREATE_BY$ = UMD.SESA_CODE
			 LEFT JOIN SY_USER_MASTER_DATA UMD2 ON T.UPDATE_BY$ = UMD2.SESA_CODE
		WHERE T.AGENT_ID = #{agentId, jdbcType=VARCHAR}
	</select>

	<update id="modifyAgent">
		DECLARE
        	CLOB_CONTENT CLOB := #{CONTENT, jdbcType=CLOB};
		BEGIN
			UPDATE MOSS_AGENT_REGISTRATION
			   SET AGENT_TYPE = #{AGENT_TYPE,jdbcType=VARCHAR},
			       SUBJECT = #{SUBJECT,jdbcType=VARCHAR},
			       GROUPS = #{GROUPS,jdbcType=VARCHAR},
			       CONTENT = CLOB_CONTENT,
			       REQUEST_URL = #{REQUEST_URL,jdbcType=VARCHAR},
			       REQUEST_HEADER = #{REQUEST_HEADER,jdbcType=VARCHAR},
			       REQUEST_BODY = #{REQUEST_BODY,jdbcType=VARCHAR},
			       UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
			       UPDATE_DATE$ = SYSDATE,
			       WELCOME_MSG = #{WELCOME_MSG, jdbcType=VARCHAR}
			 WHERE AGENT_ID = #{AGENT_ID, jdbcType=VARCHAR};
		END;
	</update>

	<delete id="deleteAgent">
		DELETE FROM MOSS_AGENT_REGISTRATION T WHERE T.AGENT_ID = #{agentId, jdbcType=VARCHAR}
	</delete>
</mapper>
