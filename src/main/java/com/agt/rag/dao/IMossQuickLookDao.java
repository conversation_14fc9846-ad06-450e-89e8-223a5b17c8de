package com.agt.rag.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IMossQuickLookDao {

    List<Map<String, String>> queryAllAvalibleTables();

    List<Map<String, String>> queryAllAvalibleColumns();

    Map<String, Object> queryTableInfo(String tableName);

    List<Map<String, String>> queryTablesByColumnName(String columnName);

    String queryCommentsByColumnName(String columnName);

    Map<String, Object> queryObjectInfo(Map<String, Object> parameterMap);

    void saveComments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryColumnsByTableName(String tableName);

    void saveHistComments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryHistComments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryKeywords(List<String> keywords);

    Object queryCommentsTemplate();

    Map<String, Object> queryTableNameByColumnName(Map<String, Object> parameterMap);

    List<String> queryColumnValueName(Map<String, Object> parameterMap);
}
