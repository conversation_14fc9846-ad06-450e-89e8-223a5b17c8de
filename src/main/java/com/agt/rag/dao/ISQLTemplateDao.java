package com.agt.rag.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ISQLTemplateDao {

    int queryReport1Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<TreeData> queryTableList();

    List<String> queryTables();

    List<String> queryTableCols();

    List<String> queryTableOptions();

    Map<String, Object> queryTemplateSQL(Map<String, Object> parameterMap);

    void saveNewTemplate(Map<String, Object> parameterMap);

    void deleteTemplate(Map<String, Object> parameterMap);

    void updateTemplate(Map<String, Object> parameterMap);

}
