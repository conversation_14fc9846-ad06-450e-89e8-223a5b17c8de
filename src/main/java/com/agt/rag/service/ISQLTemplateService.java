package com.agt.rag.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface ISQLTemplateService {

    Response queryReport1Headers(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryTableList(Map<String, Object> parameterMap);

    Response queryTableCols(Map<String, Object> parameterMap);

    Response queryTemplateSQL(Map<String, Object> parameterMap);

    Response saveNewTemplate(Map<String, Object> parameterMap);

    Response deleteTemplate(Map<String, Object> parameterMap);

    Response updateTemplate(Map<String, Object> parameterMap);
}
