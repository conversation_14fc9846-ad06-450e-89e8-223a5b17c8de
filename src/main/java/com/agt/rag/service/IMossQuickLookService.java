package com.agt.rag.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IMossQuickLookService {

    Response initPage(String userid);

    Response queryObjectInfo(Map<String, Object> parameterMap);

    Response saveComments(Map<String, Object> parameterMap);

    Response search(Map<String, Object> parameterMap);

    Response queryHistComments(Map<String, Object> parameterMap);

    Response queryKeywords(Map<String, Object> parameterMap);

    Response queryCommentsTemplate();

    Response queryColumnValueSample(Map<String, Object> parameterMap);
}
