package com.agt.rag.service.impl;

import com.agt.rag.dao.ISQLTemplateDao;
import com.agt.rag.service.ISQLTemplateService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scp.toolbox.bean.TreeData;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.DynamicDataSource;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class SQLTemplateServiceImpl implements ISQLTemplateService {

    @Resource
    private ISQLTemplateDao SQLSampleDao;

    @Resource
    private DynamicDataSource dynamicDataSource;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Headers(Map<String, Object> parameterMap) {
        String sql = "select * from (" + parameterMap.get("answer") + ") tt where 1 = 0";

        List<String> headers = new ArrayList<>();
        try (Connection con = dynamicDataSource.getConnection()) {
            ResultSet rs = con.prepareStatement(sql).executeQuery();

            ResultSetMetaData rsmd = rs.getMetaData();

            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                headers.add(rsmd.getColumnLabel(i));
            }

            rs.close();
            return response.setBody(headers);
        } catch (Exception e) {
            return response.setBody("[Exception] \r\n" + Utils.getExceptionMessage(e));
        }
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    @Cacheable(Configuration.APPLICATION_NAME + ":5m")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        // 不允许用户查看DATA_MART_DB_CONFIG中的数据信息
        if (StringUtils.containsIgnoreCase((String) parameterMap.get("answer"), "DATA_MART_DB_CONFIG")) {
            return response.setBody(page);
        }

        int total = SQLSampleDao.queryReport1Count(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(SQLSampleDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "query_result_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.agt.rag.dao.ISQLSampleDao.queryReport1", parameterMap);
    }

    @Override
    public Response queryTableList(Map<String, Object> parameterMap) {
        List<TreeData> tableList = SQLSampleDao.queryTableList();
        ObjectMapper mapper = new ObjectMapper();
        tableList.forEach(data -> {
            try {
                List<String> groupList = mapper.readValue(data.getGroups(), List.class);
                if (groupList != null && !groupList.isEmpty()) {
                    int count = groupList.size() - 1;
                    String summary = count > 0 ?
                            groupList.get(0) + " [+" + count + "]" :
                            groupList.get(0);
                    data.setGroups(summary);
                }
            } catch (Exception e) {
                data.setGroups("Invalid");
            }
        });
        return response.setBody(Utils.parseTreeNodes(tableList));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryTableCols(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("tableOptions", SQLSampleDao.queryTableOptions());
        resultMap.put("tables", SQLSampleDao.queryTables());
        resultMap.put("cols", SQLSampleDao.queryTableCols());
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryTemplateSQL(Map<String, Object> parameterMap) {
        return response.setBody(SQLSampleDao.queryTemplateSQL(parameterMap));
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveNewTemplate(Map<String, Object> parameterMap) {
        SQLSampleDao.saveNewTemplate(parameterMap);
        return response;
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response deleteTemplate(Map<String, Object> parameterMap) {
        SQLSampleDao.deleteTemplate(parameterMap);
        return response;
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response updateTemplate(Map<String, Object> parameterMap) {
        SQLSampleDao.updateTemplate(parameterMap);
        return response;
    }
}
