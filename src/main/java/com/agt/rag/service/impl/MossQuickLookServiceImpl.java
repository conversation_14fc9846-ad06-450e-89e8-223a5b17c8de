package com.agt.rag.service.impl;

import com.agt.rag.dao.IMossQuickLookDao;
import com.agt.rag.service.IMossQuickLookService;
import com.alibaba.fastjson.JSON;
import com.starter.context.bean.Response;
import com.starter.utils.MarkdownUtil;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.ansj.domain.Term;
import org.ansj.splitWord.analysis.ToAnalysis;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Scope("prototype")
@Transactional
public class MossQuickLookServiceImpl implements IMossQuickLookService {

    public static final String PARENT_CODE = "menuE07";

    @Resource
    private IMossQuickLookDao mossQuickLookDao;

    @Resource
    private Response response;

    @Override
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("allAvalibleTables", mossQuickLookDao.queryAllAvalibleTables());
        resultMap.put("allAvalibleColumns", mossQuickLookDao.queryAllAvalibleColumns());
        return response.setBody(resultMap);
    }

    private static final Map<String, String> HEADER_DISPLAY_MAP = new LinkedHashMap<>();

    static {
        HEADER_DISPLAY_MAP.put("NO.", "序号");
        HEADER_DISPLAY_MAP.put("TABLE_NAME", "表名");
        HEADER_DISPLAY_MAP.put("COLUMN_NAME", "列名");
        HEADER_DISPLAY_MAP.put("DATA_TYPE", "数据类型");
        HEADER_DISPLAY_MAP.put("BUSINESS_MEANING", "业务含义");
        HEADER_DISPLAY_MAP.put("VALUE_EXAMPLE", "值示例");
        HEADER_DISPLAY_MAP.put("ALTERNATE_TERM", "别名");
        HEADER_DISPLAY_MAP.put("COMMENTS", "备注");
        HEADER_DISPLAY_MAP.put("UPDATE_BY", "上次更新");
    }

    @Override
    public Response search(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        String type = (String) parameterMap.get("type");
        String name = (String) parameterMap.get("name");
        String[] fieldKeys = {"NO.", "COLUMN_NAME", "DATA_TYPE", "BUSINESS_MEANING", "VALUE_EXAMPLE", "ALTERNATE_TERM", "COMMENTS", "UPDATE_BY"};

        if ("COLUMN".equals(type)) {
            result.append("### 哪些表包含了 ").append(name).append(" \r\n");
            List<Map<String, String>> tables = mossQuickLookDao.queryTablesByColumnName(name);
            result.append("| 序号 | 表名 |\n");
            result.append("| - | - |\n");
            for (int i = 0; i < tables.size(); i++) {
                String tableName = tables.get(i).get("TABLE_NAME");
                result.append("|");
                result.append(i + 1);
                result.append("|");
                result.append("<a class='moss-quicklook-link' object-name='");
                result.append(tableName);
                result.append("' object-type='TABLE' href='javascript:void(0)' onclick='window.mossQuickLookEditObject(this)'>");
                result.append(tableName).append("</a>");
                result.append("|\n");
            }

            result.append("### 字段释义").append("\n");
            String columnComment = mossQuickLookDao.queryCommentsByColumnName(name);

            if (StringUtils.isNotBlank(columnComment)) {
                result.append(columnComment);
            } else {
                result.append("`NO DATA`\n\n");
            }
        } else if ("TABLE".equals(type)) {
            Map<String, Object> objectInfo = mossQuickLookDao.queryTableInfo(name);
            result.append("### <a class='moss-quicklook-link' object-name='");
            result.append(name);
            result.append("' object-type='TABLE' href='javascript:void(0)' onclick='window.mossQuickLookEditObject(this)'>");
            result.append(name).append("</a>  &nbsp;&nbsp;`TABLE`\n");

            if (objectInfo != null) {
                result.append("- <small><a style='color: var(--scp-text-color-primary);' object-name='");
                result.append(name);
                result.append("'  object-type='TABLE' href='javascript:void(0)' onclick='window.mossQuickLookQueryHistComments(this)'>*Comment by ");
                result.append(objectInfo.get("CREATE_BY"));
                result.append("@");
                result.append(objectInfo.get("CREATE_TIME"));
                result.append("* </a></small>\n");
                result.append(MarkdownUtil.addQuotePrefix(Utils.clob2String(objectInfo.get("COMMENTS"))));
                result.append("\n");
                result.append("\n");
            } else {
                result.append("`NO DATA`\n\n");
            }

            result.append("### 字段描述 \n\n");
            String tableStructure = this.convertColumnToMarkdown(mossQuickLookDao.queryColumnsByTableName(name), fieldKeys, HEADER_DISPLAY_MAP);
            result.append(tableStructure);
        }

        return response.setBody(result);
    }

    @Override
    public Response queryObjectInfo(Map<String, Object> parameterMap) {
        Map<String, Object> objectInfo = mossQuickLookDao.queryObjectInfo(parameterMap);
        if (objectInfo == null) {
            objectInfo = new HashMap<>();
            objectInfo.put("OBJECT_TYPE", parameterMap.get("objectType"));
            objectInfo.put("OBJECT_NAME", parameterMap.get("objectName"));
        }
        objectInfo.put("VALUE_EXAMPLE", JSON.parseArray((String) objectInfo.getOrDefault("VALUE_EXAMPLE", "[]")));
        return response.setBody(objectInfo);
    }

    @Override
    public Response queryColumnValueSample(Map<String, Object> parameterMap) {
        Map<String, Object> columnInfo = mossQuickLookDao.queryTableNameByColumnName(parameterMap);
        return response.setBody(mossQuickLookDao.queryColumnValueName(columnInfo));
    }

    @Override
    public Response saveComments(Map<String, Object> parameterMap) {
        parameterMap.put("valueExample", JSON.toJSONString(parameterMap.get("valueExample")));
        mossQuickLookDao.saveComments(parameterMap);
        mossQuickLookDao.saveHistComments(parameterMap);
        return response;
    }

    @Override
    public Response queryHistComments(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        List<Map<String, Object>> columns = mossQuickLookDao.queryHistComments(parameterMap);
        String objectType = (String) parameterMap.get("objectType");

        String[] headers;
        if ("COLUMN".equals(objectType)) {
            headers = new String[]{"NO.", "COLUMN_NAME", "BUSINESS_MEANING", "VALUE_EXAMPLE", "ALTERNATE_TERM", "COMMENTS", "CREATE_TIME", "CREATE_BY"};
        } else {
            headers = new String[]{"NO.", "TABLE_NAME", "COMMENTS", "CREATE_TIME", "CREATE_BY"};
        }
        if (columns == null || columns.isEmpty()) {
            return response.setBody("`NO DATA`\n\n");
        }
        result.append("| ").append(StringUtils.join(headers, " | ").replace("_", " ")).append(" |").append("\n");
        result.append("| ").append(StringUtils.repeat(" - |", headers.length)).append("\n");
        int index = 1;
        for (Map<String, Object> map : columns) {
            result.append("|");
            for (String header : headers) {
                if ("NO.".equals(header)) {
                    result.append(index++);
                } else if ("COMMENTS".equals(header)) {
                    String comments = Utils.clob2String(map.get(header));
                    if (StringUtils.isNotBlank(comments)) {
                        comments = StringUtils.replace(comments, "|", " ");
                        comments = StringUtils.replace(comments, "\r", " ");
                        comments = StringUtils.replace(comments, "\n", " ");
                        result.append(comments);
                    } else {
                        result.append(" ");
                    }
                } else if ("TABLE_NAME".equals(header) || "COLUMN_NAME".equals(header)) {
                    result.append(Utils.removeMakeDownSpliter(map.get("OBJECT_NAME")));
                } else {
                    result.append(Utils.removeMakeDownSpliter(map.get(header)));
                }
                result.append("|");
            }
            result.append("\r\n");
        }
        return response.setBody(result);
    }

    @Override
    public Response queryKeywords(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        String keywords = (String) parameterMap.get("keywords");
        Pattern pattern = Pattern.compile("[a-zA-Z0-9]");
        Matcher matcher = pattern.matcher(keywords);
        List<String> wordList = new ArrayList<>();
        if (!keywords.isEmpty()) {
            wordList.add(keywords);
            while (keywords.contains("  ")) {
                keywords = keywords.replace("  ", "");
            }
            if (keywords.contains(" ")) {
                wordList.addAll(List.of(keywords.split(" ")));
            }
            if (!matcher.find() && keywords.length() > 1) {
                List<Term> terms = ToAnalysis.parse(keywords).getTerms();
                for (Term term : terms) {
                    if (!Objects.equals(term.getNatureStr(), "u")) {
                        wordList.add(term.getName());
                    }
                }
            }
            resultList.addAll(mossQuickLookDao.queryKeywords(wordList));
        }
        result.put("words", wordList);
        result.put("result", resultList);
        return response.setBody(result);
    }

    @Override
    public Response queryCommentsTemplate() {
        return response.setBody(Utils.clob2String(mossQuickLookDao.queryCommentsTemplate()));
    }

    private String convertColumnToMarkdown(List<Map<String, Object>> columns, String[] fieldKeys, Map<String, String> displayMap) {
        StringBuilder result = new StringBuilder();
        if (columns == null || columns.isEmpty()) {
            return "`NO DATA`\n\n";
        }

        // 显示中文表头
        result.append("| ").append(StringUtils.join(Arrays.stream(fieldKeys).map(key -> displayMap.getOrDefault(key, key)).toArray(String[]::new), " | ")).append(" |").append("\n");
        result.append("| ").append(StringUtils.repeat(" - |", fieldKeys.length)).append("\n");

        int index = 1;
        for (Map<String, Object> map : columns) {
            result.append("|");
            // 英文字段名
            for (String fieldKey : fieldKeys) {
                if ("NO.".equals(fieldKey)) {
                    result.append(index++);
                } else if ("DATA_TYPE".equals(fieldKey)) {
                    result.append(Utils.removeMakeDownSpliter(map.get("DATA_TYPE")));
                    if (map.get("DATA_LENGTH") != null) {
                        result.append("(");
                        result.append(map.get("DATA_LENGTH"));
                        result.append(")");
                    }
                } else if ("COMMENTS".equals(fieldKey)) {
                    String comments = Utils.clob2String(map.get("COMMENTS"));
                    if (StringUtils.isNotBlank(comments)) {
                        comments = StringUtils.replace(comments, "|", " ");
                        comments = StringUtils.replace(comments, "\r", " ");
                        comments = StringUtils.replace(comments, "\n", " ");
                        result.append(comments);
                    } else {
                        result.append(" ");
                    }
                } else if ("UPDATE_BY".equals(fieldKey)) {
                    String comments = Utils.clob2String(map.get("COMMENTS"));
                    if (StringUtils.isNotBlank(comments)) {
                        result.append("<a class='moss-quicklook-link' object-name='").append(map.get("COLUMN_NAME")).append("' object-type='COLUMN'").append(" href='javascript:void(0)' onclick='window.mossQuickLookQueryHistComments(this)'>").append(map.get("CREATE_BY")).append("@").append(map.get("CREATE_TIME")).append("</a>");
                    } else {
                        result.append(" ");
                    }
                } else if ("VALUE_EXAMPLE".equals(fieldKey)) {
                    String valueExample = (String) map.get("VALUE_EXAMPLE");
                    if (StringUtils.isNotBlank(valueExample)) {
                        result.append(StringUtils.join(JSON.parseArray(valueExample), ", "));
                    } else {
                        result.append(" ");
                    }
                } else if (StringUtils.equals(fieldKey, "COLUMN_NAME")) {
                    result.append("<a style='color: var(--scp-text-color-primary);' object-name='");
                    result.append(map.get("COLUMN_NAME"));
                    result.append("'  object-type='COLUMN' href='javascript:void(0)' onclick='window.mossQuickLookEditObject(this)'>");
                    result.append(map.get("COLUMN_NAME"));
                    result.append("</a>");
                } else {
                    result.append(Utils.removeMakeDownSpliter(map.get(fieldKey)));
                }
                result.append("|");
            }
            result.append("\r\n");
        }
        return result.toString();
    }
}
