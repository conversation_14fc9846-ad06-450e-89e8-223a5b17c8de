package com.agt.rag;

import com.agt.rag.service.ISQLTemplateService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/agent/rag/sql_template")
@Scope("prototype")
public class SQLTemplateController extends ControllerHelper {

    @Resource
    private ISQLTemplateService SQLSampleService;

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return SQLSampleService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1_headers")
    public Response queryReport1Headers(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return SQLSampleService.queryReport1Headers(parameterMap);
    }

    @SchneiderRequestMapping(value = "/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        this.pageLoad(request);
        SQLSampleService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/query_table_list")
    public Response queryTableList(HttpServletRequest request) {
        this.pageLoad(request);
        return SQLSampleService.queryTableList(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_table_cols")
    public Response queryTableCols(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return SQLSampleService.queryTableCols(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_template_sql")
    public Response queryTemplateSQL(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return SQLSampleService.queryTemplateSQL(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_new_template")
    public Response saveNewTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return SQLSampleService.saveNewTemplate(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_template")
    public Response deleteTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return SQLSampleService.deleteTemplate(parameterMap);
    }

    @SchneiderRequestMapping(value = "/update_template")
    public Response updateTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return SQLSampleService.updateTemplate(parameterMap);
    }

}
