package com.agt.agent;

import com.agt.agent.service.IMossService;
import com.agt.agent.service.impl.MossServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/intelligent_agent/moss", parent = MossServiceImpl.PARENT_CODE)
@Scope("prototype")
public class MossController extends ControllerHelper {

    @Resource
    private IMossService mossService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.initPage(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/search")
    public Response search(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.search(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_chat_logs")
    public Response queryChatLogs(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.queryChatLogs(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_all_chat_logs")
    public Response queryAllChatLogs(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.queryAllChatLogs(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_chat_by_id")
    public Response queryChatById(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.queryChatById(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_table_tips")
    public Response queryTableTips(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.queryTableTips(parameterMap);
    }

    @SchneiderRequestMapping(value = "/create_new_conversation")
    public Response createNewConversation(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.createNewConversation(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/search_agent_id")
    public Response searchAgentId(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.searchAgentId(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_data_by_sql")
    public Response queryDataBySql(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.queryDataBySql(parameterMap);
    }

    @SchneiderRequestMapping(value = "/download_data_by_sql")
    public void downloadDataBySql(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        mossService.downloadDataBySql(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/init_chart")
    public Response initChart(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.initChart(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_chart_by_sql")
    public Response queryChartBySql(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.queryChartBySql(parameterMap);
    }

    @SchneiderRequestMapping(value = "/confirm_table")
    public Response confirmTable(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.confirmTable(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/result_evaluate")
    public Response resultEvaluate(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.resultEvaluate(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_suggest_questions")
    public Response querySuggestQuestions(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.querySuggestQuestions(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_all_agents")
    public Response queryAllAgents(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.queryAllAgents(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_agent_by_id")
    public Response queryAgentById(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.queryAgentById(parameterMap);
    }

    @SchneiderRequestMapping(value = "/add_to_archive")
    public Response addToArchive(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.addToArchive(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_my_archive")
    public Response queryMyArchive(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.queryMyArchive(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_my_archive")
    public Response deleteMyArchive(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.deleteMyArchive(parameterMap);
    }
}
