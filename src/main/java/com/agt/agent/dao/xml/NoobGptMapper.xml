<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.agent.dao.INoobGptDao">
    <select id="queryAllScenarioQueryRegAndID" resultType="java.util.Map">
		SELECT T.ID,
		       NVL(T.PREPARED_CONTENT, T.NAME) PREPARED_CONTENT
		  FROM SCPA.NOOB_GPT_SCENARIO_QUERY T
		 WHERE T.ENABLE = 'Y'
	</select>

    <select id="queryScenarioQueryByID" resultType="java.util.Map">
		SELECT T.API_URL, T.API_DESCRIPTION FROM SCPA.NOOB_GPT_SCENARIO_QUERY T WHERE T.ID = #{id, jdbcType=VARCHAR}
	</select>

    <select id="executeQuery" resultType="java.util.LinkedHashMap" flushCache="true" useCache="false">
		SELECT * FROM (
			${SQL}
		) T
		FETCH NEXT 64 ROWS ONLY
	</select>

	<select id="queryScenarioList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.ID AS KEY,
               T.NAME AS LABEL,
               T.GROUP_NAME AS GROUPS,
               T.PREPARED_CONTENT AS DATA_VALUE
         FROM NOOB_GPT_SCENARIO_QUERY T
        WHERE T.ENABLE = 'Y'
        ORDER BY T.GROUP_NAME, T.NAME
    </select>

	<insert id="saveScphApiToken">
		BEGIN
			DELETE FROM SCPH.SY_API_TOKEN T WHERE T.CREATE_DATE &lt; SYSDATE - INTERVAL '5' MINUTE;
			INSERT INTO SCPH.SY_API_TOKEN (TOKEN, USER_ID, CREATE_DATE)
			VALUES
			(#{token, jdbcType=VARCHAR}, #{userid, jdbcType=VARCHAR}, SYSDATE);
			COMMIT;
		END;
	</insert>

	<insert id="saveSoUrgingLogs">
		DECLARE
        	CLOB_CONTENT CLOB := #{logs, jdbcType=CLOB};
		BEGIN
			INSERT INTO NOOB_GPT_SO_URGING_LOGS (ID, URGING_TYPE, URGING_LINE, LOGS, CREATE_DATE$, CREATE_BY$)
			VALUES
			(#{id,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{lines,jdbcType=NUMERIC}, CLOB_CONTENT, SYSDATE, #{userid,jdbcType=VARCHAR});
		END;
	</insert>

    <select id="queryEntityByOrderInfo" resultType="java.util.Map">
        SELECT T.SALES_ORDER_NUMBER, T.SALES_ORDER_ITEM, T.ENTITY, T.MATERIAL_OWNER_NAME FROM SCPA.SO_STRUCTURE_CREATION_V T
        WHERE (T.SALES_ORDER_NUMBER, T.SALES_ORDER_ITEM) IN
        <foreach collection="orders" separator="," open="(" close=")" item="item">
            (#{item.so,jdbcType=VARCHAR}, #{item.item,jdbcType=VARCHAR})
        </foreach>
    </select>
</mapper>
