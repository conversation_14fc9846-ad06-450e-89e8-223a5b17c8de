package com.agt.agent.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class MossTreemap {
    private String name;
    private BigDecimal value;
    private List<MossTreemap> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public List<MossTreemap> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public boolean hasChildren() {
        return this.getChildren() != null && !this.getChildren().isEmpty();
    }

    public void setChildren(List<MossTreemap> children) {
        this.children = children;
    }

    // 合并两个节点
    public void add(MossTreemap addElement) {
        MossTreemap mainElement = this;

        // 再相加子节点
        while (addElement.hasChildren()) {
            List<MossTreemap> mainChildren = mainElement.getChildren();
            MossTreemap child = addElement.getChildren().get(0); // 加数节点只有一个子节点

            Optional<MossTreemap> beanOpt = mainChildren.stream().filter(b -> b.getName().equals(child.getName())).findFirst();
            if (beanOpt.isPresent()) {
                MossTreemap bean = beanOpt.get();

                // 向下移动一层
                addElement = child;
                mainElement = bean;
            } else {
                mainChildren.add(child);// 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                break; // 然后直接跳出循环, 相加结束
            }
        }
    }
}
