package com.agt.agent.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IMossService {

    Response initPage(Map<String, Object> parameterMap, Session session);

    Response search(Map<String, Object> parameterMap, Session session);

    Response createNewConversation(Map<String, Object> parameterMap, Session session);

    Response queryDataBySql(Map<String, Object> parameterMap);

    void downloadDataBySql(Map<String, Object> parameterMap, HttpServletResponse response);

    Response initChart(Map<String, Object> parameterMap);

    Response queryChartBySql(Map<String, Object> parameterMap);

    Response searchAgentId(Map<String, Object> parameterMap, Session session);

    Response confirmTable(Map<String, Object> parameterMap, Session session);

    Response resultEvaluate(Map<String, Object> parameterMap, Session session);

    Response queryTableTips(Map<String, Object> parameterMap);

    Response queryChatLogs(Map<String, Object> parameterMap);

    Response queryChatById(Map<String, Object> parameterMap);

    Response queryAllChatLogs(Map<String, Object> parameterMap);

    Response querySuggestQuestions(Map<String, Object> parameterMap);

    Response queryAllAgents(Map<String, Object> parameterMap);

    Response queryAgentById(Map<String, Object> parameterMap);

    Response addToArchive(Map<String, Object> parameterMap);

    Response queryMyArchive(Map<String, Object> parameterMap);

    Response deleteMyArchive(Map<String, Object> parameterMap);
}
